# Victor Enhanced - Dependencies
# Optimized for 8GB RAM systems and virtual environments

# ============================================================================
# CORE DEPENDENCIES (Essential for basic functionality)
# ============================================================================
requests>=2.28.0
numpy>=1.21.0
python-dotenv>=0.19.0
psutil>=5.8.0
aiohttp>=3.8.0
aiofiles>=0.8.0
sqlalchemy>=1.4.0

# ============================================================================
# AUDIO PROCESSING (Optional - for voice features)
# ============================================================================
# Note: PyAudio can be problematic on some systems
# Install separately if needed: pip install pyaudio
pyttsx3>=2.90
SpeechRecognition>=3.8.1

# ============================================================================
# COMPUTER VISION (Optional - for vision features)
# ============================================================================
opencv-python>=4.5.0
# Note: mediapipe can be large, install if needed
# mediapipe>=0.8.0

# ============================================================================
# NATURAL LANGUAGE PROCESSING
# ============================================================================
# Note: spaCy models need separate download: python -m spacy download en_core_web_sm
# spacy>=3.4.0

# ============================================================================
# MACHINE LEARNING (Lightweight versions)
# ============================================================================
scikit-learn>=1.1.0
joblib>=1.1.0

# ============================================================================
# OFFLINE AI (Optional - requires Ollama installation)
# ============================================================================
# ollama  # Install separately: curl -fsSL https://ollama.ai/install.sh | sh

# ============================================================================
# UTILITIES
# ============================================================================
rich>=12.0.0
colorama>=0.4.4
tqdm>=4.64.0
schedule>=1.1.0
watchdog>=2.1.0

# ============================================================================
# SECURITY
# ============================================================================
cryptography>=3.4.0
bcrypt>=3.2.0

# ============================================================================
# WEB SCRAPING (Optional)
# ============================================================================
beautifulsoup4>=4.11.0
# selenium>=4.0.0  # Large dependency, install if needed

# ============================================================================
# DEVELOPMENT AND TESTING
# ============================================================================
pytest>=7.0.0