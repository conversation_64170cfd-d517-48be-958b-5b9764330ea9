# Victor  - Git Ignore File

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/
victor-env/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Victor Specific
logs/*.log
data/captures/*.jpg
data/captures/*.png
data/faces/*.jpg
data/faces/*.png
data/databases/*.db
data/models/*.pkl
data/models/*.bin
assets/models/*.weights
assets/models/*.cfg
assets/models/*.names

# Temporary files
*.tmp
*.temp
temp/
tmp/

# Audio files
*.wav
*.mp3
*.m4a

# API Keys and Secrets
.env
*.key
*.pem
secrets.json

# Model files (large)
*.h5
*.pb
*.onnx
*.tflite

# Ollama models (managed separately)
ollama_models/

# Test outputs
test_output/
test_results/
