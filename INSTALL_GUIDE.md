# 🚀 Victor Enhanced - Installation Guide

## ⚠️ **Fixing "externally-managed-environment" Error**

If you're getting the `externally-managed-environment` error, here are several solutions:

---

## 🐍 **Solution 1: Virtual Environment (Recommended)**

### **Quick Setup:**
```bash
# Create virtual environment
python victor.py venv

# Activate it
source venv/bin/activate  # Linux/Mac
# or
venv\Scripts\activate     # Windows

# Install dependencies
pip install -r requirements.txt

# Start Victor
python victor.py start
```

### **Manual Setup:**
```bash
# Create virtual environment
python -m venv venv

# Activate virtual environment
source venv/bin/activate  # Linux/Mac
# or
venv\Scripts\activate     # Windows

# Install dependencies
pip install -r requirements.txt

# Test installation
python victor.py test
```

---

## 📦 **Solution 2: Using pipx (Alternative)**

```bash
# Install pipx if not available
sudo apt install pipx  # Ubuntu/Debian
# or
brew install pipx       # macOS

# Install Victor dependencies
pipx install --include-deps -r requirements.txt
```

---

## 🐍 **Solution 3: Conda Environment**

```bash
# Create conda environment
conda create -n victor python=3.9

# Activate environment
conda activate victor

# Install dependencies
pip install -r requirements.txt

# Start Victor
python victor.py start
```

---

## ⚡ **Solution 4: System Override (Not Recommended)**

```bash
# Force install to system (use with caution)
pip install -r requirements.txt --break-system-packages

# Or use user directory
pip install -r requirements.txt --user
```

---

## 🔧 **Solution 5: Minimal Installation**

If you want to try Victor with minimal dependencies:

```bash
# Install only core dependencies
pip install --user numpy requests sqlite3 asyncio pathlib psutil

# Test basic functionality
python victor.py test
```

---

## 📋 **Step-by-Step Installation**

### **1. Setup Environment**
```bash
# Choose one method above
python victor.py venv  # Recommended
```

### **2. Activate Environment**
```bash
source venv/bin/activate  # Linux/Mac
```

### **3. Install Dependencies**
```bash
pip install -r requirements.txt
```

### **4. Install Ollama (for AI)**
```bash
# Linux
curl -fsSL https://ollama.ai/install.sh | sh

# macOS
brew install ollama

# Windows - download from ollama.ai
```

### **5. Download AI Model**
```bash
ollama pull mistral:7b
```

### **6. Test Installation**
```bash
python victor.py test
```

### **7. Run Demo**
```bash
python victor.py demo
```

### **8. Start Victor**
```bash
python victor.py start
```

---

## 🐛 **Troubleshooting**

### **If virtual environment fails:**
```bash
# Try with specific Python version
python3.9 -m venv venv
# or
python3.10 -m venv venv
```

### **If pip install fails:**
```bash
# Update pip first
pip install --upgrade pip

# Then install requirements
pip install -r requirements.txt
```

### **If Ollama fails:**
```bash
# Check if Ollama is running
ollama list

# Start Ollama service
ollama serve

# Test connection
curl http://localhost:11434/api/tags
```

---

## ✅ **Verification**

After installation, verify everything works:

```bash
# Test system
python victor.py test

# Should show:
# ✓ Directory Structure: PASS
# ✓ Module Imports: PASS (if dependencies installed)
# ✓ Configuration: PASS
```

---

## 🎯 **Quick Start Commands**

Once installed:

```bash
python victor.py          # Start Victor
python victor.py demo     # Interactive demo
python victor.py test     # System test
```

---

## 💡 **Tips**

1. **Always use virtual environments** for Python projects
2. **Keep your system Python clean** - don't install packages globally
3. **Use the venv command** - `python victor.py venv` makes it easy
4. **Activate before using** - remember to activate your virtual environment

---

**🎉 Once installed, Victor Enhanced will be your powerful offline AI assistant!**
