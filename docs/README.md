# Victor Enhanced - JARVIS-like AI Personal Assistant

A comprehensive offline AI-powered personal assistant with advanced system integration, personality, learning capabilities, and JARVIS-like behavior. Powered by Ollama + <PERSON>stral for complete offline operation, optimized for 8GB RAM systems.

## Features

### 🤖 JARVIS-like Personality
- **Adaptive personality** that evolves based on interactions
- **Proactive insights** and suggestions
- **Mood-based responses** (helpful, analytical, witty, encouraging)
- **Loyalty and wit** similar to <PERSON><PERSON>VI<PERSON> from Iron Man
- **Context-aware conversations** with memory

### 🧠 Offline AI Intelligence (Ollama + Mistral)
- **Complete offline operation** - no internet required for AI
- **Mistral 7B model** optimized for 8GB RAM systems
- **Advanced conversation abilities** with personality
- **Learning from interactions** to improve over time
- **No API costs** - everything runs locally

### 🎤 Advanced Audio Processing
- Wake word detection with Porcupine ("Victor")
- High-quality speech recognition
- Multiple TTS engines (Google Cloud TTS, pyttsx3)
- Voice authentication and profiling
- Noise reduction and audio enhancement

### 👁️ Computer Vision System
- Real-time object detection with YOLO
- Face detection and recognition
- Pose estimation and gesture recognition
- Scene analysis and description
- Image capture and processing

### 🧠 Intelligent Learning System
- **Continuous learning** from daily interactions
- **Pattern recognition** in user behavior
- **Knowledge base** that grows over time
- **Preference adaptation** based on usage
- **Neural network training** for performance improvement

### 💻 System Integration
- Comprehensive system commands
- File and folder operations
- Process management
- Network operations
- Cross-platform support (Windows, Linux, macOS)

### 🔒 Security & Authentication
- Multi-factor authentication
- Voice biometric authentication
- Data encryption and secure storage
- Session management
- Security event logging

### 🧠 Intelligent Knowledge Base
- Adaptive learning from interactions
- Web search integration
- Fact storage and retrieval
- Knowledge graph construction
- Confidence-based responses

## Installation

### 🚀 Automated Installation (Recommended)
```bash
# Clone the repository
git clone <repository-url>
cd Victor-main

# Run the automated installer
python install_victor.py
```

The installer will:
- ✅ Check system requirements (8GB RAM optimized)
- ✅ Install Python dependencies
- ✅ Download and setup Ollama + Mistral 7B model
- ✅ Configure spaCy language models
- ✅ Create optimized configuration files
- ✅ Set up directory structure
- ✅ Test the installation

### 📋 System Requirements
- **Python 3.8+** (required)
- **8GB RAM minimum** (optimized for this)
- **10GB free disk space** (for models)
- **Microphone and speakers** (for voice interaction)
- **Camera** (optional, for vision features)
- **No internet required** for AI after installation

### 🛠️ Manual Installation
If you prefer manual setup:

#### Step 1: Install Dependencies
```bash
pip install -r requirements.txt
```

#### Step 2: Install Ollama
```bash
# Linux/macOS
curl -fsSL https://ollama.ai/install.sh | sh

# Windows: Download from https://ollama.ai/download
```

#### Step 3: Setup Mistral Model
```bash
# Start Ollama service
ollama serve

# Download Mistral 7B (optimized for 8GB RAM)
ollama pull mistral:7b

# Test the model
ollama run mistral:7b "Hello, respond with just 'OK'"
```

#### Step 4: Install spaCy Model
```bash
python -m spacy download en_core_web_sm
```

#### Step 5: Setup Environment (Optional)
Create a `.env` file for optional features:
```env
# Ollama Configuration (automatically detected)
OLLAMA_MODEL=mistral:7b
OLLAMA_HOST=http://localhost:11434

# Optional API Keys (for enhanced features)
PICOVOICE_ACCESS_KEY=your_picovoice_key
OPENWEATHER_API_KEY=your_weather_api_key

# Performance Settings (8GB RAM Optimized)
MAX_MEMORY_USAGE=6442450944
ENABLE_GPU=false
```

## Quick Start

### 🎯 Test Installation
```bash
# Test all components
python test_victor.py

# Run interactive demo
python demo_victor_jarvis.py
```

### 🚀 Start Victor Enhanced
```bash
# Start with full voice interaction
python start_victor.py

# Start in text-only mode (for testing)
python victor_enhanced.py --text-mode
```

### 🎭 JARVIS-like Interaction Examples
```bash
# Wake Victor and give commands
"Victor, good morning!"
"Victor, what's the weather like?"
"Victor, show me system information"
"Victor, tell me something interesting about AI"
"Victor, what do you think about space exploration?"
"Victor, learn this: Python is my favorite programming language"
```

## Configuration

Victor Enhanced uses a comprehensive configuration system. The main configuration file is located at `config/victor_config.json`.

### Key Configuration Sections:

#### Audio Configuration
```json
{
  "audio": {
    "wake_word_model_path": "Models/Victor_en_windows_v3_0_0.ppn",
    "voice_profile": "en-US-Wavenet-D",
    "sample_rate": 16000,
    "timeout": 5.0
  }
}
```

#### Security Configuration
```json
{
  "security": {
    "enable_authentication": true,
    "authentication_method": "voice",
    "max_failed_attempts": 3,
    "session_timeout": 3600
  }
}
```

#### System Configuration
```json
{
  "system": {
    "enable_file_operations": true,
    "safe_mode": true,
    "allowed_directories": ["~/Documents", "~/Downloads"]
  }
}
```

## Usage Examples

### Voice Commands

#### System Control
- "Victor, shutdown the computer"
- "Victor, restart the system"
- "Victor, increase the volume"
- "Victor, lock the screen"

#### File Operations
- "Victor, create a new folder called Projects"
- "Victor, list the files in my Documents folder"
- "Victor, delete the file test.txt"

#### Information Queries
- "Victor, what's the weather like today?"
- "Victor, tell me about artificial intelligence"
- "Victor, what time is it?"

#### Vision Commands
- "Victor, what do you see?"
- "Victor, detect faces in the camera"
- "Victor, describe the scene"
- "Victor, take a picture"

### Programmatic Usage
```python
from victor_enhanced import VictorEnhanced
import asyncio

async def main():
    victor = VictorEnhanced()

    # Process a command
    response = await victor.process_command("What's the weather like?")
    print(response)

    # Start the main loop
    await victor.run()

asyncio.run(main())
```

## Architecture

### Module Structure
```
victor_enhanced.py          # Main application
├── modules/
│   ├── audio_module.py     # Audio processing
│   ├── vision_module.py    # Computer vision
│   ├── nlp_module.py       # Natural language processing
│   ├── system_module.py    # System integration
│   ├── knowledge_module.py # Knowledge base
│   └── security_module.py  # Security management
├── config/
│   ├── settings.py         # Configuration management
│   └── victor_config.json  # Default configuration
└── data/                   # Data storage
    ├── faces/              # Known faces for recognition
    ├── captures/           # Captured images
    └── *.db               # Database files
```

### Key Components

1. **VictorEnhanced**: Main orchestrator class
2. **EnhancedAudioProcessor**: Handles all audio I/O
3. **AdvancedVisionSystem**: Computer vision processing
4. **AdvancedNLPProcessor**: Natural language understanding
5. **SystemCommandModule**: System integration
6. **IntelligentKnowledgeBase**: Learning and knowledge storage
7. **SecurityManager**: Authentication and security

## Security Features

### Authentication Methods
- **Voice Authentication**: Biometric voice recognition
- **Password Authentication**: Traditional username/password
- **Session Management**: Secure session tokens

### Data Protection
- **Encryption**: AES encryption for sensitive data
- **Secure Storage**: Encrypted database storage
- **Access Control**: Permission-based operations
- **Audit Logging**: Comprehensive security event logging

### Safe Mode
When enabled, Safe Mode restricts potentially dangerous operations:
- System shutdown/restart commands
- File deletion operations
- Process termination
- Network configuration changes

## Troubleshooting

### Common Issues

#### Audio Issues
```bash
# Check audio devices
python -c "import pyaudio; pa = pyaudio.PyAudio(); [print(f'{i}: {pa.get_device_info_by_index(i)[\"name\"]}') for i in range(pa.get_device_count())]"

# Test microphone
python -c "import speech_recognition as sr; r = sr.Recognizer(); print('Say something...'); print(r.recognize_google(r.listen(sr.Microphone())))"
```

#### Vision Issues
```bash
# Test camera
python -c "import cv2; cap = cv2.VideoCapture(0); ret, frame = cap.read(); print('Camera working:' if ret else 'Camera not found'); cap.release()"
```

#### Dependencies
```bash
# Reinstall dependencies
pip install -r requirements.txt --force-reinstall

# Check for missing modules
python start_victor.py
```

### Performance Optimization

#### Memory Usage
- Adjust `max_memory_usage` in configuration
- Enable/disable features based on system capabilities
- Use lighter models for resource-constrained systems

#### CPU Usage
- Reduce vision processing frequency
- Use smaller language models
- Disable unused features

## Development

### Adding New Features

1. **Create a new module** in the `modules/` directory
2. **Update configuration** in `config/settings.py`
3. **Integrate with main class** in `victor_enhanced.py`
4. **Add tests** and documentation

### Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Acknowledgments

- OpenAI for GPT models
- Google Cloud for TTS services
- Picovoice for wake word detection
- Ultralytics for YOLO models
- spaCy for NLP processing
- OpenCV for computer vision

## Support

For support and questions:
- Create an issue on GitHub
- Check the troubleshooting section
- Review the configuration documentation

---

**Victor Enhanced** - Your intelligent personal assistant with advanced AI capabilities.
