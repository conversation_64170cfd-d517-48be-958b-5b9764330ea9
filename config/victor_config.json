{"audio": {"wake_word_model_path": "Models/Victor_en_windows_v3_0_0.ppn", "voice_profile": "en-US-Wavenet-D", "sample_rate": 16000, "chunk_size": 1024, "channels": 1, "enable_noise_reduction": true, "volume_threshold": 0.5, "timeout": 5.0, "phrase_timeout": 1.0}, "vision": {"camera_index": 0, "resolution": [640, 480], "fps": 30, "face_detection_model": "haarcascade_frontalface_default.xml", "object_detection_model": "yolov5x", "pose_estimation_model": "yolov8n-pose.pt", "confidence_threshold": 0.5, "enable_face_recognition": true, "enable_gesture_recognition": true}, "nlp": {"language_model": "ollama:mistral:7b", "spacy_model": "en_core_web_sm", "max_response_length": 150, "temperature": 0.7, "enable_context_awareness": true, "offline_mode": true}, "system": {"enable_file_operations": true, "enable_process_control": true, "enable_network_operations": true, "enable_system_commands": true, "safe_mode": true, "max_file_size": 52428800}, "security": {"enable_authentication": true, "authentication_method": "voice", "max_failed_attempts": 3, "session_timeout": 3600, "enable_encryption": true, "log_security_events": true, "require_confirmation_for_sensitive": true, "sensitive_operations": ["file_delete", "system_shutdown", "process_kill", "network_config", "user_management"]}, "knowledge": {"database_path": "data/victor_knowledge.db", "enable_online_learning": true, "enable_web_search": true, "cache_size": 1000, "learning_rate": 0.01, "confidence_threshold": 0.8, "max_context_length": 4096, "enable_fact_checking": true}, "api": {"openai_api_key": "", "google_cloud_key_path": "", "weather_api_key": "", "news_api_key": "", "search_api_key": "", "email_smtp_server": "smtp.gmail.com", "email_smtp_port": 587}, "logging": {"log_level": "INFO", "log_file": "logs/victor.log", "max_log_size": 10485760, "backup_count": 5, "enable_console_logging": true, "enable_file_logging": true, "log_format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s"}, "debug_mode": false, "performance_monitoring": true, "auto_save_interval": 300, "max_memory_usage": 2147483648, "performance": {"max_memory_usage": 6442450944, "enable_caching": true, "cache_size": 512, "optimize_for_ram": true}, "personality": {"enable_learning": true, "enable_proactive_insights": true, "enable_mood_adaptation": true, "personality_evolution_rate": 0.1}}