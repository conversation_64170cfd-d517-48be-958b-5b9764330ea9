#!/usr/bin/env python3
"""
<PERSON>ced - Installation Fix Script
Fixes common installation issues including "externally-managed-environment"
"""

import sys
import subprocess
import os
from pathlib import Path

def print_header():
    """Print header"""
    print("🔧 Victor Enhanced - Installation Fix Script")
    print("=" * 60)
    print("Fixing common installation issues...")
    print()

def check_python_version():
    """Check Python version"""
    print("🐍 Checking Python version...")
    version = sys.version_info
    print(f"   Python {version.major}.{version.minor}.{version.micro}")

    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("❌ Python 3.8+ required")
        return False

    print("✅ Python version OK")
    return True

def check_virtual_environment():
    """Check if in virtual environment"""
    print("\n🏠 Checking virtual environment...")

    in_venv = (
        hasattr(sys, 'real_prefix') or
        (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix) or
        os.environ.get('VIRTUAL_ENV') is not None
    )

    if in_venv:
        print("✅ Running in virtual environment")
        venv_path = os.environ.get('VIRTUAL_ENV', 'Unknown')
        print(f"   Environment: {venv_path}")
        return True
    else:
        print("⚠️  Not in virtual environment")
        return False

def create_virtual_environment():
    """Create virtual environment"""
    print("\n🔧 Creating virtual environment...")

    venv_path = Path("venv")

    if venv_path.exists():
        print("✅ Virtual environment already exists")
        return True

    try:
        subprocess.run([sys.executable, "-m", "venv", str(venv_path)], check=True)
        print("✅ Virtual environment created successfully")

        # Show activation instructions
        print("\n🚀 To activate the virtual environment:")
        if sys.platform == "win32":
            print(f"   {venv_path}\\Scripts\\activate")
        else:
            print(f"   source {venv_path}/bin/activate")

        return True

    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to create virtual environment: {e}")
        return False

def install_minimal_dependencies():
    """Install minimal dependencies"""
    print("\n📦 Installing minimal dependencies...")

    minimal_packages = [
        "requests>=2.28.0",
        "numpy>=1.21.0",
        "python-dotenv>=0.19.0",
        "rich>=12.0.0",
        "colorama>=0.4.4"
    ]

    success_count = 0

    for package in minimal_packages:
        try:
            print(f"   Installing {package}...")
            subprocess.run([sys.executable, "-m", "pip", "install", package],
                          check=True, capture_output=True)
            print(f"   ✅ {package} installed")
            success_count += 1
        except subprocess.CalledProcessError as e:
            print(f"   ❌ Failed to install {package}")

    if success_count >= 3:
        print(f"\n✅ {success_count}/{len(minimal_packages)} packages installed")
        print("🎉 Minimal installation successful!")
        return True
    else:
        print(f"\n❌ Only {success_count}/{len(minimal_packages)} packages installed")
        return False

def test_installation():
    """Test if Victor can be imported"""
    print("\n🧪 Testing Victor installation...")

    try:
        # Test basic imports
        import requests
        import numpy
        print("✅ Core dependencies working")

        # Test Victor import
        sys.path.insert(0, str(Path.cwd() / "src"))
        try:
            import victor_enhanced
            print("✅ Victor Enhanced can be imported")
        except ImportError as e:
            print(f"⚠️  Victor import issue: {e}")
            print("✅ Core dependencies work, Victor should still function")

        return True

    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

def show_solutions():
    """Show installation solutions"""
    print("\n💡 INSTALLATION SOLUTIONS:")
    print()

    print("🔧 Method 1: Virtual Environment (Recommended)")
    print("   python fix_installation.py")
    print("   source venv/bin/activate")
    print("   pip install -r requirements-minimal.txt")
    print()

    print("🔧 Method 2: User Installation")
    print("   pip install --user -r requirements-minimal.txt")
    print()

    print("🔧 Method 3: Force System Installation (Not Recommended)")
    print("   pip install --break-system-packages -r requirements-minimal.txt")
    print()

    print("🔧 Method 4: Using pipx")
    print("   pipx install -r requirements-minimal.txt")
    print()

def main():
    """Main function"""
    print_header()

    # Check Python version
    if not check_python_version():
        sys.exit(1)

    # Check virtual environment
    in_venv = check_virtual_environment()

    if not in_venv:
        print("\n🔧 FIXING: Creating virtual environment...")
        if create_virtual_environment():
            print("\n✅ Virtual environment created!")
            print("🚀 Next steps:")
            print("   1. Activate: source venv/bin/activate")
            print("   2. Install: pip install -r requirements-minimal.txt")
            print("   3. Test: python victor.py test")
        else:
            print("\n❌ Failed to create virtual environment")
            show_solutions()
            sys.exit(1)
    else:
        print("\n🔧 FIXING: Installing dependencies...")
        if install_minimal_dependencies():
            if test_installation():
                print("\n🎉 SUCCESS! Victor Enhanced is ready!")
                print("🚀 Try: python victor.py demo")
            else:
                print("\n⚠️  Installation completed but testing failed")
                print("💡 Try: python victor.py test")
        else:
            print("\n❌ Installation failed")
            show_solutions()

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n⏹️  Installation cancelled by user")
    except Exception as e:
        print(f"\n\n❌ Unexpected error: {e}")
        show_solutions()
