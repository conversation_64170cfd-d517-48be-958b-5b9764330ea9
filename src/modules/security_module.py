"""
Security Manager Module for <PERSON> 
Handles authentication, authorization, and security operations
"""

import asyncio
import logging
import hashlib
import secrets
import json
import time
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List
from pathlib import Path
import sqlite3

try:
    import bcrypt
    BCRYPT_AVAILABLE = True
except ImportError:
    BCRYPT_AVAILABLE = False
    logging.warning("bcrypt not available")

try:
    from cryptography.fernet import Fernet
    from cryptography.hazmat.primitives import hashes
    from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
    import base64
    CRYPTOGRAPHY_AVAILABLE = True
except ImportError:
    CRYPTOGRAPHY_AVAILABLE = False
    logging.warning("cryptography not available")

logger = logging.getLogger(__name__)

class SecurityManager:
    """Comprehensive security management for Victor """
    
    def __init__(self, config):
        self.config = config
        self.security_config = config.security
        
        # Security state
        self.authenticated_users = {}
        self.failed_attempts = {}
        self.security_events = []
        self.session_tokens = {}
        
        # Encryption
        self.encryption_key = None
        self.cipher_suite = None
        
        # Database for security data
        self.security_db_path = "data/security.db"
        self.security_conn = None
        
        # Initialize components
        self._initialize_security_database()
        self._initialize_encryption()
        self._load_security_settings()
    
    def _initialize_security_database(self):
        """Initialize security database"""
        try:
            Path(self.security_db_path).parent.mkdir(parents=True, exist_ok=True)
            
            self.security_conn = sqlite3.connect(self.security_db_path, check_same_thread=False)
            
            # Create security tables
            cursor = self.security_conn.cursor()
            
            # Users table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS users (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    username TEXT UNIQUE NOT NULL,
                    password_hash TEXT NOT NULL,
                    salt TEXT NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    last_login TIMESTAMP,
                    failed_attempts INTEGER DEFAULT 0,
                    locked_until TIMESTAMP,
                    permissions TEXT,
                    voice_profile_hash TEXT,
                    biometric_data TEXT
                )
            ''')
            
            # Security events table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS security_events (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    event_type TEXT NOT NULL,
                    user_id TEXT,
                    description TEXT,
                    ip_address TEXT,
                    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    severity TEXT DEFAULT 'INFO',
                    additional_data TEXT
                )
            ''')
            
            # Sessions table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS sessions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    session_token TEXT UNIQUE NOT NULL,
                    user_id TEXT NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    expires_at TIMESTAMP NOT NULL,
                    last_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    ip_address TEXT,
                    user_agent TEXT
                )
            ''')
            
            self.security_conn.commit()
            logger.info("Security database initialized")
            
        except Exception as e:
            logger.error(f"Failed to initialize security database: {e}")
            raise
    
    def _initialize_encryption(self):
        """Initialize encryption components"""
        try:
            if not CRYPTOGRAPHY_AVAILABLE:
                logger.warning("Encryption not available - cryptography library missing")
                return
            
            # Load or generate encryption key
            key_file = Path("data/encryption.key")
            
            if key_file.exists():
                with open(key_file, 'rb') as f:
                    self.encryption_key = f.read()
            else:
                # Generate new key
                self.encryption_key = Fernet.generate_key()
                key_file.parent.mkdir(parents=True, exist_ok=True)
                with open(key_file, 'wb') as f:
                    f.write(self.encryption_key)
                # Secure the key file
                key_file.chmod(0o600)
            
            self.cipher_suite = Fernet(self.encryption_key)
            logger.info("Encryption initialized")
            
        except Exception as e:
            logger.error(f"Failed to initialize encryption: {e}")
    
    def _load_security_settings(self):
        """Load security settings and create default admin user if needed"""
        try:
            cursor = self.security_conn.cursor()
            cursor.execute('SELECT COUNT(*) FROM users')
            user_count = cursor.fetchone()[0]
            
            if user_count == 0:
                # Create default admin user
                self._create_default_admin()
            
        except Exception as e:
            logger.error(f"Error loading security settings: {e}")
    
    def _create_default_admin(self):
        """Create default admin user"""
        try:
            default_password = "victor_admin_2024"  # Should be changed immediately
            
            password_hash, salt = self._hash_password(default_password)
            
            cursor = self.security_conn.cursor()
            cursor.execute('''
                INSERT INTO users (username, password_hash, salt, permissions)
                VALUES (?, ?, ?, ?)
            ''', ("admin", password_hash, salt, json.dumps(["all"])))
            
            self.security_conn.commit()
            
            logger.warning("Default admin user created with password: victor_admin_2024")
            logger.warning("Please change the default password immediately!")
            
        except Exception as e:
            logger.error(f"Failed to create default admin user: {e}")
    
    def _hash_password(self, password: str) -> tuple:
        """Hash password with salt"""
        if BCRYPT_AVAILABLE:
            salt = bcrypt.gensalt()
            password_hash = bcrypt.hashpw(password.encode('utf-8'), salt)
            return password_hash.decode('utf-8'), salt.decode('utf-8')
        else:
            # Fallback to hashlib
            salt = secrets.token_hex(32)
            password_hash = hashlib.pbkdf2_hmac('sha256', password.encode('utf-8'), salt.encode('utf-8'), 100000)
            return password_hash.hex(), salt
    
    def _verify_password(self, password: str, stored_hash: str, salt: str) -> bool:
        """Verify password against stored hash"""
        if BCRYPT_AVAILABLE:
            return bcrypt.checkpw(password.encode('utf-8'), stored_hash.encode('utf-8'))
        else:
            # Fallback verification
            password_hash = hashlib.pbkdf2_hmac('sha256', password.encode('utf-8'), salt.encode('utf-8'), 100000)
            return password_hash.hex() == stored_hash
    
    async def authenticate_user(self, username: str = None, password: str = None, voice_data: bytes = None) -> bool:
        """
        Authenticate user using various methods
        
        Args:
            username: Username for password authentication
            password: Password for password authentication
            voice_data: Voice data for voice authentication
            
        Returns:
            bool: True if authentication successful
        """
        try:
            if not self.security_config.enable_authentication:
                return True
            
            auth_method = self.security_config.authentication_method
            
            if auth_method == "password" and username and password:
                return await self._authenticate_password(username, password)
            elif auth_method == "voice" and voice_data:
                return await self._authenticate_voice(voice_data)
            elif auth_method == "biometric":
                return await self._authenticate_biometric()
            else:
                # Default to simple authentication for demo
                logger.info("Using simplified authentication")
                return True
                
        except Exception as e:
            logger.error(f"Authentication error: {e}")
            await self._log_security_event("AUTH_ERROR", description=str(e), severity="ERROR")
            return False
    
    async def _authenticate_password(self, username: str, password: str) -> bool:
        """Authenticate using username and password"""
        try:
            # Check failed attempts
            if self._is_user_locked(username):
                await self._log_security_event("AUTH_LOCKED", user_id=username, description="Account locked due to failed attempts")
                return False
            
            cursor = self.security_conn.cursor()
            cursor.execute('''
                SELECT password_hash, salt, failed_attempts 
                FROM users 
                WHERE username = ?
            ''', (username,))
            
            result = cursor.fetchone()
            if not result:
                await self._increment_failed_attempts(username)
                await self._log_security_event("AUTH_FAILED", user_id=username, description="User not found")
                return False
            
            stored_hash, salt, failed_attempts = result
            
            if self._verify_password(password, stored_hash, salt):
                # Reset failed attempts on successful login
                cursor.execute('''
                    UPDATE users 
                    SET failed_attempts = 0, last_login = CURRENT_TIMESTAMP 
                    WHERE username = ?
                ''', (username,))
                self.security_conn.commit()
                
                # Create session
                session_token = await self._create_session(username)
                self.authenticated_users[username] = {
                    'session_token': session_token,
                    'login_time': datetime.now(),
                    'permissions': self._get_user_permissions(username)
                }
                
                await self._log_security_event("AUTH_SUCCESS", user_id=username, description="Password authentication successful")
                return True
            else:
                await self._increment_failed_attempts(username)
                await self._log_security_event("AUTH_FAILED", user_id=username, description="Invalid password")
                return False
                
        except Exception as e:
            logger.error(f"Password authentication error: {e}")
            return False
    
    async def _authenticate_voice(self, voice_data: bytes) -> bool:
        """Authenticate using voice biometrics (placeholder)"""
        try:
            # This is a simplified implementation
            # In practice, you'd use voice recognition algorithms
            
            voice_hash = hashlib.sha256(voice_data).hexdigest()
            
            cursor = self.security_conn.cursor()
            cursor.execute('SELECT username FROM users WHERE voice_profile_hash = ?', (voice_hash,))
            
            result = cursor.fetchone()
            if result:
                username = result[0]
                session_token = await self._create_session(username)
                self.authenticated_users[username] = {
                    'session_token': session_token,
                    'login_time': datetime.now(),
                    'permissions': self._get_user_permissions(username)
                }
                
                await self._log_security_event("AUTH_SUCCESS", user_id=username, description="Voice authentication successful")
                return True
            
            await self._log_security_event("AUTH_FAILED", description="Voice authentication failed")
            return False
            
        except Exception as e:
            logger.error(f"Voice authentication error: {e}")
            return False
    
    async def _authenticate_biometric(self) -> bool:
        """Authenticate using biometric data (placeholder)"""
        try:
            # Placeholder for biometric authentication
            # Would integrate with fingerprint readers, facial recognition, etc.
            
            await self._log_security_event("AUTH_SUCCESS", description="Biometric authentication successful")
            return True
            
        except Exception as e:
            logger.error(f"Biometric authentication error: {e}")
            return False
    
    def _is_user_locked(self, username: str) -> bool:
        """Check if user account is locked"""
        try:
            cursor = self.security_conn.cursor()
            cursor.execute('''
                SELECT failed_attempts, locked_until 
                FROM users 
                WHERE username = ?
            ''', (username,))
            
            result = cursor.fetchone()
            if not result:
                return False
            
            failed_attempts, locked_until = result
            
            # Check if locked due to failed attempts
            if failed_attempts >= self.security_config.max_failed_attempts:
                return True
            
            # Check if explicitly locked with timestamp
            if locked_until:
                lock_time = datetime.fromisoformat(locked_until)
                if datetime.now() < lock_time:
                    return True
            
            return False
            
        except Exception as e:
            logger.error(f"Error checking user lock status: {e}")
            return False
    
    async def _increment_failed_attempts(self, username: str):
        """Increment failed login attempts for user"""
        try:
            cursor = self.security_conn.cursor()
            cursor.execute('''
                UPDATE users 
                SET failed_attempts = failed_attempts + 1 
                WHERE username = ?
            ''', (username,))
            
            # Lock account if max attempts reached
            cursor.execute('SELECT failed_attempts FROM users WHERE username = ?', (username,))
            result = cursor.fetchone()
            
            if result and result[0] >= self.security_config.max_failed_attempts:
                lock_until = datetime.now() + timedelta(minutes=30)  # Lock for 30 minutes
                cursor.execute('''
                    UPDATE users 
                    SET locked_until = ? 
                    WHERE username = ?
                ''', (lock_until.isoformat(), username))
                
                await self._log_security_event("ACCOUNT_LOCKED", user_id=username, description="Account locked due to failed attempts", severity="WARNING")
            
            self.security_conn.commit()
            
        except Exception as e:
            logger.error(f"Error incrementing failed attempts: {e}")
    
    async def _create_session(self, username: str) -> str:
        """Create a new session for authenticated user"""
        try:
            session_token = secrets.token_urlsafe(32)
            expires_at = datetime.now() + timedelta(seconds=self.security_config.session_timeout)
            
            cursor = self.security_conn.cursor()
            cursor.execute('''
                INSERT INTO sessions (session_token, user_id, expires_at)
                VALUES (?, ?, ?)
            ''', (session_token, username, expires_at.isoformat()))
            
            self.security_conn.commit()
            
            self.session_tokens[session_token] = {
                'username': username,
                'expires_at': expires_at,
                'created_at': datetime.now()
            }
            
            return session_token
            
        except Exception as e:
            logger.error(f"Error creating session: {e}")
            return None
    
    def _get_user_permissions(self, username: str) -> List[str]:
        """Get user permissions"""
        try:
            cursor = self.security_conn.cursor()
            cursor.execute('SELECT permissions FROM users WHERE username = ?', (username,))
            
            result = cursor.fetchone()
            if result and result[0]:
                return json.loads(result[0])
            
            return ["basic"]
            
        except Exception as e:
            logger.error(f"Error getting user permissions: {e}")
            return ["basic"]
    
    async def _log_security_event(self, event_type: str, user_id: str = None, description: str = "", severity: str = "INFO", additional_data: Dict = None):
        """Log security events"""
        try:
            if not self.security_config.log_security_events:
                return
            
            cursor = self.security_conn.cursor()
            cursor.execute('''
                INSERT INTO security_events (event_type, user_id, description, severity, additional_data)
                VALUES (?, ?, ?, ?, ?)
            ''', (event_type, user_id, description, severity, json.dumps(additional_data) if additional_data else None))
            
            self.security_conn.commit()
            
            # Also log to application logger
            log_message = f"Security Event: {event_type} - {description}"
            if user_id:
                log_message += f" (User: {user_id})"
            
            if severity == "ERROR":
                logger.error(log_message)
            elif severity == "WARNING":
                logger.warning(log_message)
            else:
                logger.info(log_message)
            
        except Exception as e:
            logger.error(f"Error logging security event: {e}")
    
    async def handle_security_command(self, analysis: Dict[str, Any]) -> str:
        """Handle security-related commands"""
        try:
            text = analysis.get('original_text', '').lower()
            
            if 'change password' in text:
                return await self._handle_password_change(analysis)
            elif 'logout' in text:
                return await self._handle_logout(analysis)
            elif 'security status' in text:
                return await self._get_security_status()
            elif 'lock account' in text:
                return await self._handle_account_lock(analysis)
            else:
                return "Security command not recognized."
                
        except Exception as e:
            logger.error(f"Error handling security command: {e}")
            return f"Security command error: {str(e)}"
    
    async def _handle_password_change(self, analysis: Dict[str, Any]) -> str:
        """Handle password change request"""
        # This would require additional input handling for new password
        return "Password change functionality requires additional implementation for secure password input."
    
    async def _handle_logout(self, analysis: Dict[str, Any]) -> str:
        """Handle logout request"""
        try:
            # Clear all active sessions
            self.authenticated_users.clear()
            self.session_tokens.clear()
            
            # Clear sessions from database
            cursor = self.security_conn.cursor()
            cursor.execute('DELETE FROM sessions WHERE expires_at < CURRENT_TIMESTAMP')
            self.security_conn.commit()
            
            await self._log_security_event("LOGOUT", description="User logged out")
            return "You have been logged out successfully."
            
        except Exception as e:
            logger.error(f"Logout error: {e}")
            return f"Logout error: {str(e)}"
    
    async def _get_security_status(self) -> str:
        """Get current security status"""
        try:
            active_sessions = len(self.authenticated_users)
            
            cursor = self.security_conn.cursor()
            cursor.execute('SELECT COUNT(*) FROM security_events WHERE timestamp > datetime("now", "-24 hours")')
            recent_events = cursor.fetchone()[0]
            
            cursor.execute('SELECT COUNT(*) FROM users WHERE failed_attempts > 0')
            users_with_failed_attempts = cursor.fetchone()[0]
            
            status = f"Security Status:\n"
            status += f"- Active sessions: {active_sessions}\n"
            status += f"- Security events (24h): {recent_events}\n"
            status += f"- Users with failed attempts: {users_with_failed_attempts}\n"
            status += f"- Authentication enabled: {self.security_config.enable_authentication}\n"
            status += f"- Encryption enabled: {self.security_config.enable_encryption}"
            
            return status
            
        except Exception as e:
            logger.error(f"Error getting security status: {e}")
            return f"Security status error: {str(e)}"
    
    async def _handle_account_lock(self, analysis: Dict[str, Any]) -> str:
        """Handle account lock request"""
        return "Account lock functionality requires admin privileges and additional implementation."
    
    def encrypt_data(self, data: str) -> str:
        """Encrypt sensitive data"""
        try:
            if not self.cipher_suite:
                return data  # Return unencrypted if encryption not available
            
            encrypted_data = self.cipher_suite.encrypt(data.encode('utf-8'))
            return base64.b64encode(encrypted_data).decode('utf-8')
            
        except Exception as e:
            logger.error(f"Encryption error: {e}")
            return data
    
    def decrypt_data(self, encrypted_data: str) -> str:
        """Decrypt sensitive data"""
        try:
            if not self.cipher_suite:
                return encrypted_data  # Return as-is if encryption not available
            
            encrypted_bytes = base64.b64decode(encrypted_data.encode('utf-8'))
            decrypted_data = self.cipher_suite.decrypt(encrypted_bytes)
            return decrypted_data.decode('utf-8')
            
        except Exception as e:
            logger.error(f"Decryption error: {e}")
            return encrypted_data
    
    async def shutdown(self):
        """Cleanup security module resources"""
        try:
            if self.security_conn:
                self.security_conn.close()
            
            logger.info("Security module shutdown complete")
            
        except Exception as e:
            logger.error(f"Error during security shutdown: {e}")
    
    def __del__(self):
        """Destructor to ensure database cleanup"""
        try:
            if hasattr(self, 'security_conn') and self.security_conn:
                self.security_conn.close()
        except:
            pass
