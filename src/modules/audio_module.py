"""
 Audio Processing Module for <PERSON>
Handles speech recognition, text-to-speech, wake word detection, and audio analysis
"""

import asyncio
import logging
import tempfile
import os
import numpy as np
import pyaudio
import speech_recognition as sr
import threading
from typing import Optional, Tuple, Dict, Any
from pathlib import Path

try:
    import pvporcupine
    PORCUPINE_AVAILABLE = True
except ImportError:
    PORCUPINE_AVAILABLE = False
    logging.warning("Porcupine wake word detection not available")

try:
    from google.cloud import texttospeech as tts
    from google.api_core.exceptions import InvalidArgument
    from google.auth.exceptions import RefreshError
    TTS_AVAILABLE = True
except ImportError:
    TTS_AVAILABLE = False
    logging.warning("Google Cloud TTS not available")

try:
    import pyttsx3
    PYTTSX3_AVAILABLE = True
except ImportError:
    PYTTSX3_AVAILABLE = False
    logging.warning("pyttsx3 not available")

try:
    from playsound import playsound
    PLAYSOUND_AVAILABLE = True
except ImportError:
    PLAYSOUND_AVAILABLE = False
    logging.warning("playsound not available")

logger = logging.getLogger(__name__)

class AudioProcessor:
    """ audio processing with multiple TTS engines and  features"""
    
    def __init__(self, config):
        self.config = config
        self.audio_config = config.audio
        self.recognizer = sr.Recognizer()
        self.microphone = None
        self.porcupine = None
        self.audio_stream = None
        self.pa = None
        
        # TTS clients
        self.tts_client = None
        self.pyttsx3_engine = None
        
        # Audio processing state
        self.is_listening = False
        self.wake_word_detected = False
        self.audio_lock = threading.Lock()
        
        # Initialize components
        self._initialize_audio_system()
        self._initialize_tts_engines()
        self._initialize_wake_word_detection()
    
    def _initialize_audio_system(self):
        """Initialize PyAudio and microphone"""
        try:
            self.pa = pyaudio.PyAudio()
            
            # Find the best microphone
            self.microphone = sr.Microphone()
            
            # Adjust for ambient noise
            with self.microphone as source:
                self.recognizer.adjust_for_ambient_noise(source, duration=1)
                
            logger.info("Audio system initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize audio system: {e}")
            raise
    
    def _initialize_tts_engines(self):
        """Initialize text-to-speech engines"""
        # Google Cloud TTS
        if TTS_AVAILABLE:
            try:
                self.tts_client = tts.TextToSpeechClient()
                logger.info("Google Cloud TTS initialized")
            except Exception as e:
                logger.warning(f"Failed to initialize Google Cloud TTS: {e}")
        
        # pyttsx3 as fallback
        if PYTTSX3_AVAILABLE:
            try:
                self.pyttsx3_engine = pyttsx3.init()
                
                # Configure pyttsx3
                voices = self.pyttsx3_engine.getProperty('voices')
                if voices:
                    # Try to find a good voice
                    for voice in voices:
                        if 'english' in voice.name.lower() or 'en' in voice.id.lower():
                            self.pyttsx3_engine.setProperty('voice', voice.id)
                            break
                
                # Set speech rate and volume
                self.pyttsx3_engine.setProperty('rate', 180)
                self.pyttsx3_engine.setProperty('volume', 0.9)
                
                logger.info("pyttsx3 TTS initialized")
                
            except Exception as e:
                logger.warning(f"Failed to initialize pyttsx3: {e}")
    
    def _initialize_wake_word_detection(self):
        """Initialize wake word detection with Porcupine"""
        if not PORCUPINE_AVAILABLE:
            logger.warning("Wake word detection not available - Porcupine not installed")
            return
        
        try:
            access_key = os.getenv("PICOVOICE_ACCESS_KEY")
            if not access_key:
                logger.warning("PICOVOICE_ACCESS_KEY not found - wake word detection disabled")
                return
            
            keyword_path = self.audio_config.wake_word_model_path
            if not Path(keyword_path).exists():
                logger.warning(f"Wake word model not found: {keyword_path}")
                return
            
            self.porcupine = pvporcupine.create(
                access_key=access_key,
                keyword_paths=[keyword_path]
            )
            
            # Create audio stream for wake word detection
            self.audio_stream = self.pa.open(
                rate=self.porcupine.sample_rate,
                channels=1,
                format=pyaudio.paInt16,
                input=True,
                frames_per_buffer=self.porcupine.frame_length
            )
            
            logger.info("Wake word detection initialized")
            
        except Exception as e:
            logger.error(f"Failed to initialize wake word detection: {e}")
    
    async def speak(self, text: str, voice_profile: Optional[str] = None) -> bool:
        """
        Convert text to speech using the best available TTS engine
        
        Args:
            text: Text to speak
            voice_profile: Optional voice profile to use
            
        Returns:
            bool: True if successful, False otherwise
        """
        if not text or not isinstance(text, str):
            logger.warning("Invalid text provided for TTS")
            return False
        
        try:
            # Try Google Cloud TTS first
            if self.tts_client:
                success = await self._speak_google_tts(text, voice_profile)
                if success:
                    return True
            
            # Fallback to pyttsx3
            if self.pyttsx3_engine:
                return await self._speak_pyttsx3(text)
            
            logger.error("No TTS engine available")
            return False
            
        except Exception as e:
            logger.error(f"Error in speak method: {e}")
            return False
    
    async def _speak_google_tts(self, text: str, voice_profile: Optional[str] = None) -> bool:
        """Speak using Google Cloud TTS"""
        try:
            voice_name = voice_profile or self.audio_config.voice_profile
            
            synthesis_input = tts.SynthesisInput(text=text)
            voice = tts.VoiceSelectionParams(
                language_code="en-US",
                name=voice_name
            )
            audio_config = tts.AudioConfig(
                audio_encoding=tts.AudioEncoding.MP3
            )
            
            # Synthesize speech
            response = await asyncio.to_thread(
                self.tts_client.synthesize_speech,
                input=synthesis_input,
                voice=voice,
                audio_config=audio_config
            )
            
            # Play audio
            if PLAYSOUND_AVAILABLE:
                with tempfile.NamedTemporaryFile(delete=False, suffix=".mp3") as temp_audio:
                    temp_audio.write(response.audio_content)
                    temp_audio_path = temp_audio.name
                
                await asyncio.to_thread(playsound, temp_audio_path)
                os.remove(temp_audio_path)
                
                return True
            else:
                logger.warning("Cannot play audio - playsound not available")
                return False
                
        except (InvalidArgument, RefreshError) as e:
            logger.error(f"Google TTS error: {e}")
            return False
        except Exception as e:
            logger.error(f"Unexpected error in Google TTS: {e}")
            return False
    
    async def _speak_pyttsx3(self, text: str) -> bool:
        """Speak using pyttsx3"""
        try:
            def speak_sync():
                self.pyttsx3_engine.say(text)
                self.pyttsx3_engine.runAndWait()
            
            await asyncio.to_thread(speak_sync)
            return True
            
        except Exception as e:
            logger.error(f"pyttsx3 TTS error: {e}")
            return False
    
    async def listen(self, timeout: Optional[float] = None) -> Optional[str]:
        """
        Listen for speech and convert to text
        
        Args:
            timeout: Maximum time to listen (seconds)
            
        Returns:
            str: Recognized text or None if failed
        """
        try:
            timeout = timeout or self.audio_config.timeout
            
            with self.microphone as source:
                logger.debug("Listening for speech...")
                audio = await asyncio.to_thread(
                    self.recognizer.listen,
                    source,
                    timeout=timeout,
                    phrase_time_limit=self.audio_config.phrase_timeout
                )
            
            # Recognize speech
            text = await asyncio.to_thread(
                self.recognizer.recognize_google,
                audio
            )
            
            logger.info(f"Recognized: {text}")
            return text.lower()
            
        except sr.WaitTimeoutError:
            logger.debug("Listening timeout")
            return None
        except sr.UnknownValueError:
            logger.debug("Could not understand audio")
            return None
        except sr.RequestError as e:
            logger.error(f"Speech recognition error: {e}")
            return None
        except Exception as e:
            logger.error(f"Unexpected error in listen: {e}")
            return None
    
    async def wait_for_wake_word(self) -> bool:
        """
        Wait for wake word detection
        
        Returns:
            bool: True if wake word detected, False otherwise
        """
        if not self.porcupine or not self.audio_stream:
            logger.warning("Wake word detection not available")
            return False
        
        try:
            logger.debug("Waiting for wake word...")
            
            while True:
                pcm = self.audio_stream.read(self.porcupine.frame_length, exception_on_overflow=False)
                pcm_array = np.frombuffer(pcm, dtype=np.int16)
                
                keyword_index = await asyncio.to_thread(self.porcupine.process, pcm_array)
                
                if keyword_index >= 0:
                    logger.info("Wake word detected!")
                    return True
                
                # Allow other tasks to run
                await asyncio.sleep(0.01)
                
        except Exception as e:
            logger.error(f"Error in wake word detection: {e}")
            return False
    
    async def listen_for_command(self) -> Optional[str]:
        """
        Complete workflow: wait for wake word, then listen for command
        
        Returns:
            str: Command text or None if failed
        """
        try:
            # Wait for wake word
            if self.porcupine:
                wake_detected = await self.wait_for_wake_word()
                if not wake_detected:
                    return None
                
                # Acknowledge wake word
                await self.speak("Yes, Sir?")
            
            # Listen for command
            command = await self.listen()
            return command
            
        except Exception as e:
            logger.error(f"Error in listen_for_command: {e}")
            return None
    
    async def analyze_audio_emotion(self, audio_data: np.ndarray) -> Dict[str, Any]:
        """
        Analyze emotion from audio data
        
        Args:
            audio_data: Audio data as numpy array
            
        Returns:
            dict: Emotion analysis results
        """
        # Placeholder for emotion analysis
        # Would implement with audio emotion recognition model
        return {
            "emotion": "neutral",
            "confidence": 0.5,
            "energy": float(np.mean(np.abs(audio_data))),
            "pitch": "medium"
        }
    
    async def set_voice_profile(self, profile_name: str) -> bool:
        """
        Set voice profile for TTS
        
        Args:
            profile_name: Name of voice profile
            
        Returns:
            bool: True if successful
        """
        try:
            self.audio_config.voice_profile = profile_name
            logger.info(f"Voice profile set to: {profile_name}")
            return True
        except Exception as e:
            logger.error(f"Failed to set voice profile: {e}")
            return False
    
    async def shutdown(self):
        """Cleanup audio resources"""
        try:
            if self.audio_stream:
                self.audio_stream.close()
            
            if self.porcupine:
                self.porcupine.delete()
            
            if self.pa:
                self.pa.terminate()
            
            logger.info("Audio module shutdown complete")
            
        except Exception as e:
            logger.error(f"Error during audio shutdown: {e}")
    
    def __del__(self):
        """Destructor to ensure cleanup"""
        try:
            if hasattr(self, 'audio_stream') and self.audio_stream:
                self.audio_stream.close()
            if hasattr(self, 'porcupine') and self.porcupine:
                self.porcupine.delete()
            if hasattr(self, 'pa') and self.pa:
                self.pa.terminate()
        except:
            pass
