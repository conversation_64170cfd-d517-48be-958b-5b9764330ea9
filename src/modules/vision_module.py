"""
 Vision System Module for <PERSON> 
Handles computer vision tasks including object detection, face recognition, and scene analysis
"""

import asyncio
import logging
import cv2
import numpy as np
from typing import Dict, Any, List, Optional, Tuple
from pathlib import Path
import tempfile
import os

try:
    import torch
    import torchvision.transforms as transforms
    TORCH_AVAILABLE = True
except ImportError:
    TORCH_AVAILABLE = False
    logging.warning("PyTorch not available")

try:
    from ultralytics import YOLO
    ULTRALYTICS_AVAILABLE = True
except ImportError:
    ULTRALYTICS_AVAILABLE = False
    logging.warning("Ultralytics YOLO not available")

try:
    import mediapipe as mp
    MEDIAPIPE_AVAILABLE = True
except ImportError:
    MEDIAPIPE_AVAILABLE = False
    logging.warning("MediaPipe not available")

try:
    import face_recognition
    FACE_RECOGNITION_AVAILABLE = True
except ImportError:
    FACE_RECOGNITION_AVAILABLE = False
    logging.warning("face_recognition not available")

logger = logging.getLogger(__name__)

class VisionSystem:
    """ computer vision system with multiple detection capabilities"""
    
    def __init__(self, config):
        self.config = config
        self.vision_config = config.vision
        
        # Camera setup
        self.camera = None
        self.camera_active = False
        
        # Detection models
        self.face_cascade = None
        self.yolo_model = None
        self.pose_model = None
        self.hand_model = None
        
        # MediaPipe models
        self.mp_face_detection = None
        self.mp_pose = None
        self.mp_hands = None
        self.mp_drawing = None
        
        # Face recognition
        self.known_faces = {}
        self.face_encodings = []
        self.face_names = []
        
        # Initialize components
        self._initialize_camera()
        self._initialize_models()
        self._load_known_faces()
    
    def _initialize_camera(self):
        """Initialize camera for video capture"""
        try:
            self.camera = cv2.VideoCapture(self.vision_config.camera_index)
            
            if self.camera.isOpened():
                # Set camera properties
                self.camera.set(cv2.CAP_PROP_FRAME_WIDTH, self.vision_config.resolution[0])
                self.camera.set(cv2.CAP_PROP_FRAME_HEIGHT, self.vision_config.resolution[1])
                self.camera.set(cv2.CAP_PROP_FPS, self.vision_config.fps)
                
                logger.info(f"Camera initialized: {self.vision_config.resolution} @ {self.vision_config.fps}fps")
            else:
                logger.warning("Failed to open camera")
                
        except Exception as e:
            logger.error(f"Error initializing camera: {e}")
    
    def _initialize_models(self):
        """Initialize computer vision models"""
        try:
            # OpenCV face detection
            face_cascade_path = cv2.data.haarcascades + self.vision_config.face_detection_model
            if Path(face_cascade_path).exists():
                self.face_cascade = cv2.CascadeClassifier(face_cascade_path)
                logger.info("OpenCV face cascade loaded")
            
            # YOLO object detection
            if ULTRALYTICS_AVAILABLE:
                try:
                    self.yolo_model = YOLO(self.vision_config.object_detection_model)
                    logger.info(f"YOLO model loaded: {self.vision_config.object_detection_model}")
                except Exception as e:
                    logger.warning(f"Failed to load YOLO model: {e}")
            
            # Pose estimation
            if ULTRALYTICS_AVAILABLE:
                try:
                    self.pose_model = YOLO(self.vision_config.pose_estimation_model)
                    logger.info(f"Pose model loaded: {self.vision_config.pose_estimation_model}")
                except Exception as e:
                    logger.warning(f"Failed to load pose model: {e}")
            
            # MediaPipe models
            if MEDIAPIPE_AVAILABLE:
                self.mp_face_detection = mp.solutions.face_detection
                self.mp_pose = mp.solutions.pose
                self.mp_hands = mp.solutions.hands
                self.mp_drawing = mp.solutions.drawing_utils
                logger.info("MediaPipe models initialized")
                
        except Exception as e:
            logger.error(f"Error initializing vision models: {e}")
    
    def _load_known_faces(self):
        """Load known faces for recognition"""
        try:
            if not FACE_RECOGNITION_AVAILABLE:
                return
            
            faces_dir = Path("data/faces")
            if not faces_dir.exists():
                faces_dir.mkdir(parents=True)
                logger.info("Created faces directory")
                return
            
            for face_file in faces_dir.glob("*.jpg"):
                try:
                    image = face_recognition.load_image_file(str(face_file))
                    encodings = face_recognition.face_encodings(image)
                    
                    if encodings:
                        self.face_encodings.append(encodings[0])
                        self.face_names.append(face_file.stem)
                        logger.info(f"Loaded face: {face_file.stem}")
                        
                except Exception as e:
                    logger.warning(f"Failed to load face {face_file}: {e}")
            
            logger.info(f"Loaded {len(self.face_encodings)} known faces")
            
        except Exception as e:
            logger.error(f"Error loading known faces: {e}")
    
    async def process_vision_command(self, analysis: Dict[str, Any]) -> str:
        """
        Process vision-related commands
        
        Args:
            analysis: Command analysis from NLP module
            
        Returns:
            str: Description of what was seen/detected
        """
        try:
            text = analysis.get('original_text', '').lower()
            
            if 'face' in text:
                return await self.detect_faces()
            elif 'object' in text:
                return await self.detect_objects()
            elif 'pose' in text or 'posture' in text:
                return await self.analyze_pose()
            elif 'hand' in text or 'gesture' in text:
                return await self.detect_hands()
            elif 'scene' in text or 'describe' in text:
                return await self.analyze_scene()
            elif 'take' in text and 'picture' in text:
                return await self.capture_image()
            else:
                return await self.general_vision_analysis()
                
        except Exception as e:
            logger.error(f"Error processing vision command: {e}")
            return f"Vision processing error: {str(e)}"
    
    async def detect_faces(self) -> str:
        """Detect and recognize faces"""
        try:
            if not self.camera or not self.camera.isOpened():
                return "Camera not available for face detection."
            
            # Capture frame
            ret, frame = self.camera.read()
            if not ret:
                return "Failed to capture image from camera."
            
            faces_detected = []
            
            # OpenCV face detection
            if self.face_cascade:
                gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
                faces = self.face_cascade.detectMultiScale(
                    gray, 
                    scaleFactor=1.1, 
                    minNeighbors=5, 
                    minSize=(30, 30)
                )
                
                for (x, y, w, h) in faces:
                    faces_detected.append({
                        'method': 'opencv',
                        'bbox': (x, y, w, h),
                        'confidence': 0.8  # OpenCV doesn't provide confidence
                    })
            
            # Face recognition
            recognized_faces = []
            if FACE_RECOGNITION_AVAILABLE and self.face_encodings:
                rgb_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
                face_locations = face_recognition.face_locations(rgb_frame)
                face_encodings = face_recognition.face_encodings(rgb_frame, face_locations)
                
                for encoding in face_encodings:
                    matches = face_recognition.compare_faces(self.face_encodings, encoding)
                    name = "Unknown"
                    
                    if True in matches:
                        match_index = matches.index(True)
                        name = self.face_names[match_index]
                    
                    recognized_faces.append(name)
            
            # Generate response
            if faces_detected:
                response = f"I detected {len(faces_detected)} face(s)."
                if recognized_faces:
                    known_faces = [name for name in recognized_faces if name != "Unknown"]
                    if known_faces:
                        response += f" I recognized: {', '.join(known_faces)}."
                    unknown_count = recognized_faces.count("Unknown")
                    if unknown_count > 0:
                        response += f" {unknown_count} unknown face(s)."
                return response
            else:
                return "I don't see any faces in the current view."
                
        except Exception as e:
            logger.error(f"Error in face detection: {e}")
            return f"Face detection error: {str(e)}"
    
    async def detect_objects(self) -> str:
        """Detect objects in the current view"""
        try:
            if not self.camera or not self.camera.isOpened():
                return "Camera not available for object detection."
            
            if not self.yolo_model:
                return "Object detection model not available."
            
            # Capture frame
            ret, frame = self.camera.read()
            if not ret:
                return "Failed to capture image from camera."
            
            # Run YOLO detection
            results = await asyncio.to_thread(self.yolo_model, frame)
            
            detected_objects = []
            for result in results:
                boxes = result.boxes
                if boxes is not None:
                    for box in boxes:
                        confidence = float(box.conf[0])
                        if confidence >= self.vision_config.confidence_threshold:
                            class_id = int(box.cls[0])
                            class_name = self.yolo_model.names[class_id]
                            detected_objects.append({
                                'name': class_name,
                                'confidence': confidence
                            })
            
            if detected_objects:
                # Group by object type
                object_counts = {}
                for obj in detected_objects:
                    name = obj['name']
                    if name in object_counts:
                        object_counts[name] += 1
                    else:
                        object_counts[name] = 1
                
                descriptions = []
                for name, count in object_counts.items():
                    if count == 1:
                        descriptions.append(f"a {name}")
                    else:
                        descriptions.append(f"{count} {name}s")
                
                return f"I can see: {', '.join(descriptions)}."
            else:
                return "I don't see any recognizable objects in the current view."
                
        except Exception as e:
            logger.error(f"Error in object detection: {e}")
            return f"Object detection error: {str(e)}"
    
    async def analyze_pose(self) -> str:
        """Analyze human pose/posture"""
        try:
            if not self.camera or not self.camera.isOpened():
                return "Camera not available for pose analysis."
            
            # Capture frame
            ret, frame = self.camera.read()
            if not ret:
                return "Failed to capture image from camera."
            
            pose_description = "No pose detected."
            
            # MediaPipe pose detection
            if MEDIAPIPE_AVAILABLE and self.mp_pose:
                with self.mp_pose.Pose(
                    static_image_mode=True,
                    model_complexity=1,
                    enable_segmentation=False,
                    min_detection_confidence=0.5
                ) as pose:
                    rgb_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
                    results = pose.process(rgb_frame)
                    
                    if results.pose_landmarks:
                        # Analyze pose (simplified)
                        landmarks = results.pose_landmarks.landmark
                        
                        # Check if person is standing or sitting
                        left_hip = landmarks[23]
                        right_hip = landmarks[24]
                        left_knee = landmarks[25]
                        right_knee = landmarks[26]
                        
                        hip_y = (left_hip.y + right_hip.y) / 2
                        knee_y = (left_knee.y + right_knee.y) / 2
                        
                        if knee_y > hip_y + 0.1:
                            pose_description = "I see a person in a sitting position."
                        else:
                            pose_description = "I see a person in a standing position."
            
            # YOLO pose estimation as fallback
            elif self.pose_model:
                results = await asyncio.to_thread(self.pose_model, frame)
                
                if results and len(results) > 0:
                    pose_description = "I detected human pose keypoints."
            
            return pose_description
            
        except Exception as e:
            logger.error(f"Error in pose analysis: {e}")
            return f"Pose analysis error: {str(e)}"
    
    async def detect_hands(self) -> str:
        """Detect hands and gestures"""
        try:
            if not self.camera or not self.camera.isOpened():
                return "Camera not available for hand detection."
            
            if not MEDIAPIPE_AVAILABLE:
                return "Hand detection not available."
            
            # Capture frame
            ret, frame = self.camera.read()
            if not ret:
                return "Failed to capture image from camera."
            
            with self.mp_hands.Hands(
                static_image_mode=True,
                max_num_hands=2,
                min_detection_confidence=0.5
            ) as hands:
                rgb_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
                results = hands.process(rgb_frame)
                
                if results.multi_hand_landmarks:
                    hand_count = len(results.multi_hand_landmarks)
                    return f"I detected {hand_count} hand(s) in the view."
                else:
                    return "I don't see any hands in the current view."
                    
        except Exception as e:
            logger.error(f"Error in hand detection: {e}")
            return f"Hand detection error: {str(e)}"
    
    async def analyze_scene(self) -> str:
        """Analyze the overall scene"""
        try:
            if not self.camera or not self.camera.isOpened():
                return "Camera not available for scene analysis."
            
            # Capture frame
            ret, frame = self.camera.read()
            if not ret:
                return "Failed to capture image from camera."
            
            # Combine multiple detection methods
            scene_elements = []
            
            # Object detection
            if self.yolo_model:
                results = await asyncio.to_thread(self.yolo_model, frame)
                for result in results:
                    boxes = result.boxes
                    if boxes is not None:
                        for box in boxes:
                            confidence = float(box.conf[0])
                            if confidence >= self.vision_config.confidence_threshold:
                                class_id = int(box.cls[0])
                                class_name = self.yolo_model.names[class_id]
                                scene_elements.append(class_name)
            
            # Face detection
            if self.face_cascade:
                gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
                faces = self.face_cascade.detectMultiScale(gray, scaleFactor=1.1, minNeighbors=5)
                if len(faces) > 0:
                    scene_elements.append(f"{len(faces)} person(s)")
            
            # Basic scene analysis
            height, width = frame.shape[:2]
            brightness = np.mean(cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY))
            
            lighting = "well-lit" if brightness > 100 else "dimly lit"
            
            if scene_elements:
                unique_elements = list(set(scene_elements))
                description = f"I see a {lighting} scene containing: {', '.join(unique_elements)}."
            else:
                description = f"I see a {lighting} scene, but I cannot identify specific objects."
            
            return description
            
        except Exception as e:
            logger.error(f"Error in scene analysis: {e}")
            return f"Scene analysis error: {str(e)}"
    
    async def capture_image(self) -> str:
        """Capture and save an image"""
        try:
            if not self.camera or not self.camera.isOpened():
                return "Camera not available for image capture."
            
            # Capture frame
            ret, frame = self.camera.read()
            if not ret:
                return "Failed to capture image from camera."
            
            # Save image
            timestamp = asyncio.get_event_loop().time()
            filename = f"capture_{int(timestamp)}.jpg"
            filepath = Path("data/captures") / filename
            filepath.parent.mkdir(parents=True, exist_ok=True)
            
            cv2.imwrite(str(filepath), frame)
            
            return f"Image captured and saved as {filename}."
            
        except Exception as e:
            logger.error(f"Error capturing image: {e}")
            return f"Image capture error: {str(e)}"
    
    async def general_vision_analysis(self) -> str:
        """General vision analysis combining multiple methods"""
        try:
            # Combine face detection, object detection, and scene analysis
            results = []
            
            face_result = await self.detect_faces()
            if "detected" in face_result or "recognized" in face_result:
                results.append(face_result)
            
            object_result = await self.detect_objects()
            if "can see" in object_result:
                results.append(object_result)
            
            if results:
                return " ".join(results)
            else:
                return "I can see the camera view, but I cannot identify specific objects or people."
                
        except Exception as e:
            logger.error(f"Error in general vision analysis: {e}")
            return f"Vision analysis error: {str(e)}"
    
    async def add_known_face(self, name: str, image_path: str) -> str:
        """Add a new known face for recognition"""
        try:
            if not FACE_RECOGNITION_AVAILABLE:
                return "Face recognition not available."
            
            image = face_recognition.load_image_file(image_path)
            encodings = face_recognition.face_encodings(image)
            
            if not encodings:
                return "No face found in the provided image."
            
            # Save the face encoding
            self.face_encodings.append(encodings[0])
            self.face_names.append(name)
            
            # Save image to faces directory
            faces_dir = Path("data/faces")
            faces_dir.mkdir(parents=True, exist_ok=True)
            
            import shutil
            shutil.copy2(image_path, faces_dir / f"{name}.jpg")
            
            return f"Added {name} to known faces."
            
        except Exception as e:
            logger.error(f"Error adding known face: {e}")
            return f"Failed to add known face: {str(e)}"
    
    async def shutdown(self):
        """Cleanup vision system resources"""
        try:
            if self.camera and self.camera.isOpened():
                self.camera.release()
            
            cv2.destroyAllWindows()
            logger.info("Vision module shutdown complete")
            
        except Exception as e:
            logger.error(f"Error during vision shutdown: {e}")
    
    def __del__(self):
        """Destructor to ensure camera cleanup"""
        try:
            if hasattr(self, 'camera') and self.camera and self.camera.isOpened():
                self.camera.release()
        except:
            pass
