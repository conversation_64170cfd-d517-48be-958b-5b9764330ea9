"""
Personality and Learning Module for <PERSON> personality traits, proactive behavior, and continuous learning
"""

import asyncio
import logging
import random
import json
import sqlite3
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional
from pathlib import Path
import schedule
import time

try:
    import requests
    from bs4 import BeautifulSoup
    WEB_SCRAPING_AVAILABLE = True
except ImportError:
    WEB_SCRAPING_AVAILABLE = False
    logging.warning("Web scraping not available")

try:
    import joblib
    import numpy as np
    from sklearn.feature_extraction.text import TfidfVectorizer
    from sklearn.cluster import KMeans
    ML_AVAILABLE = True
except ImportError:
    ML_AVAILABLE = False
    logging.warning("ML libraries not available")

logger = logging.getLogger(__name__)

class PersonalityEngine:
    """<PERSON>'s personality engine with adaptive behavior"""
    
    def __init__(self, config):
        self.config = config
        
        # Core personality traits (0.0 to 1.0)
        self.traits = {
            "helpfulness": 0.95,
            "curiosity": 0.85,
            "wit": 0.75,
            "loyalty": 0.98,
            "proactiveness": 0.80,
            "analytical": 0.90,
            "empathy": 0.70,
            "confidence": 0.85
        }
        
        # Dynamic mood states
        self.moods = {
            "helpful": {"energy": 0.8, "focus": 0.9, "sociability": 0.8},
            "analytical": {"energy": 0.7, "focus": 0.95, "sociability": 0.6},
            "witty": {"energy": 0.9, "focus": 0.7, "sociability": 0.9},
            "focused": {"energy": 0.6, "focus": 1.0, "sociability": 0.5},
            "encouraging": {"energy": 0.9, "focus": 0.8, "sociability": 0.95}
        }
        
        self.current_mood = "helpful"
        self.mood_history = []
        
        # Interests and knowledge domains
        self.interests = {
            "technology": 0.95,
            "science": 0.90,
            "programming": 0.95,
            "AI": 0.98,
            "space": 0.85,
            "cybersecurity": 0.88,
            "automation": 0.92,
            "efficiency": 0.90,
            "learning": 0.95,
            "innovation": 0.88
        }
        
        # Behavioral patterns
        self.behavior_patterns = {
            "greeting_style": "formal_friendly",
            "response_length": "moderate",
            "technical_depth": "adaptive",
            "humor_frequency": 0.3,
            "proactive_suggestions": 0.4
        }
        
        # Learning database
        self.personality_db_path = "data/victor_personality.db"
        self.personality_db = None
        
        # Initialize components
        self._initialize_personality_database()
        self._load_personality_state()
    
    def _initialize_personality_database(self):
        """Initialize personality database"""
        try:
            Path(self.personality_db_path).parent.mkdir(parents=True, exist_ok=True)
            self.personality_db = sqlite3.connect(self.personality_db_path, check_same_thread=False)
            
            cursor = self.personality_db.cursor()
            
            # Personality evolution table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS personality_evolution (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    trait_name TEXT NOT NULL,
                    old_value REAL NOT NULL,
                    new_value REAL NOT NULL,
                    trigger_event TEXT,
                    confidence REAL DEFAULT 0.5
                )
            ''')
            
            # Mood tracking table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS mood_tracking (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    mood TEXT NOT NULL,
                    duration_minutes INTEGER,
                    trigger_context TEXT,
                    effectiveness_score REAL
                )
            ''')
            
            # Interest evolution table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS interest_evolution (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    interest_topic TEXT NOT NULL,
                    interest_level REAL NOT NULL,
                    interaction_count INTEGER DEFAULT 1,
                    last_interaction TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # Proactive insights table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS proactive_insights (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    insight_type TEXT NOT NULL,
                    insight_content TEXT NOT NULL,
                    user_reaction TEXT,
                    effectiveness_score REAL,
                    context_data TEXT
                )
            ''')
            
            self.personality_db.commit()
            logger.info("Personality database initialized")
            
        except Exception as e:
            logger.error(f"Failed to initialize personality database: {e}")
    
    def _load_personality_state(self):
        """Load personality state from database"""
        try:
            if not self.personality_db:
                return
            
            cursor = self.personality_db.cursor()
            
            # Load latest trait values
            cursor.execute('''
                SELECT trait_name, new_value 
                FROM personality_evolution 
                WHERE timestamp = (
                    SELECT MAX(timestamp) 
                    FROM personality_evolution pe2 
                    WHERE pe2.trait_name = personality_evolution.trait_name
                )
            ''')
            
            for trait_name, value in cursor.fetchall():
                if trait_name in self.traits:
                    self.traits[trait_name] = value
            
            # Load interest levels
            cursor.execute('''
                SELECT interest_topic, interest_level 
                FROM interest_evolution 
                WHERE timestamp = (
                    SELECT MAX(timestamp) 
                    FROM interest_evolution ie2 
                    WHERE ie2.interest_topic = interest_evolution.interest_topic
                )
            ''')
            
            for topic, level in cursor.fetchall():
                self.interests[topic] = level
            
            logger.info("Personality state loaded from database")
            
        except Exception as e:
            logger.error(f"Error loading personality state: {e}")
    
    async def adapt_personality(self, interaction_data: Dict[str, Any]):
        """Adapt personality based on user interactions"""
        try:
            user_satisfaction = interaction_data.get('satisfaction', 0.5)
            interaction_type = interaction_data.get('type', 'general')
            topics = interaction_data.get('topics', [])
            
            # Adapt traits based on satisfaction
            if user_satisfaction > 0.8:
                # Positive feedback - reinforce current behavior
                if interaction_type == 'helpful':
                    await self._evolve_trait('helpfulness', 0.02)
                elif interaction_type == 'witty':
                    await self._evolve_trait('wit', 0.02)
                elif interaction_type == 'analytical':
                    await self._evolve_trait('analytical', 0.02)
            
            elif user_satisfaction < 0.3:
                # Negative feedback - adjust behavior
                if self.current_mood == 'witty':
                    await self._evolve_trait('wit', -0.01)
                    await self.change_mood('helpful')
            
            # Update interests based on topics
            for topic in topics:
                if topic in self.interests:
                    await self._evolve_interest(topic, 0.01)
                else:
                    # New interest discovered
                    self.interests[topic] = 0.3
                    await self._evolve_interest(topic, 0.0)
            
        except Exception as e:
            logger.error(f"Error adapting personality: {e}")
    
    async def _evolve_trait(self, trait_name: str, change: float):
        """Evolve a personality trait"""
        try:
            if trait_name not in self.traits:
                return
            
            old_value = self.traits[trait_name]
            new_value = max(0.0, min(1.0, old_value + change))
            
            if abs(new_value - old_value) > 0.001:  # Only record significant changes
                self.traits[trait_name] = new_value
                
                # Record evolution
                cursor = self.personality_db.cursor()
                cursor.execute('''
                    INSERT INTO personality_evolution 
                    (trait_name, old_value, new_value, trigger_event)
                    VALUES (?, ?, ?, ?)
                ''', (trait_name, old_value, new_value, 'user_interaction'))
                
                self.personality_db.commit()
                
                logger.info(f"Trait {trait_name} evolved: {old_value:.3f} -> {new_value:.3f}")
        
        except Exception as e:
            logger.error(f"Error evolving trait {trait_name}: {e}")
    
    async def _evolve_interest(self, topic: str, change: float):
        """Evolve interest in a topic"""
        try:
            old_level = self.interests.get(topic, 0.0)
            new_level = max(0.0, min(1.0, old_level + change))
            
            self.interests[topic] = new_level
            
            # Record in database
            cursor = self.personality_db.cursor()
            cursor.execute('''
                INSERT OR REPLACE INTO interest_evolution 
                (interest_topic, interest_level, interaction_count, last_interaction)
                VALUES (?, ?, 
                    COALESCE((SELECT interaction_count FROM interest_evolution WHERE interest_topic = ?) + 1, 1),
                    CURRENT_TIMESTAMP)
            ''', (topic, new_level, topic))
            
            self.personality_db.commit()
            
        except Exception as e:
            logger.error(f"Error evolving interest in {topic}: {e}")
    
    async def change_mood(self, new_mood: str, context: str = ""):
        """Change Victor's mood"""
        try:
            if new_mood not in self.moods:
                return False
            
            old_mood = self.current_mood
            self.current_mood = new_mood
            
            # Record mood change
            cursor = self.personality_db.cursor()
            cursor.execute('''
                INSERT INTO mood_tracking (mood, trigger_context)
                VALUES (?, ?)
            ''', (new_mood, context))
            
            self.personality_db.commit()
            
            # Update mood history
            self.mood_history.append({
                'mood': new_mood,
                'timestamp': datetime.now(),
                'context': context
            })
            
            # Keep only recent mood history
            if len(self.mood_history) > 50:
                self.mood_history = self.mood_history[-50:]
            
            logger.info(f"Mood changed from {old_mood} to {new_mood}")
            return True
            
        except Exception as e:
            logger.error(f"Error changing mood: {e}")
            return False
    
    def get_personality_response_style(self) -> Dict[str, Any]:
        """Get current personality-based response style"""
        current_mood_traits = self.moods.get(self.current_mood, {})
        
        return {
            'formality': 0.7 if self.traits['loyalty'] > 0.8 else 0.5,
            'enthusiasm': current_mood_traits.get('energy', 0.7),
            'detail_level': current_mood_traits.get('focus', 0.8),
            'humor_probability': self.traits['wit'] * 0.4,
            'proactive_probability': self.traits['proactiveness'] * 0.5,
            'empathy_level': self.traits['empathy'],
            'confidence_level': self.traits['confidence']
        }
    
    async def generate_proactive_insight(self, user_context: Dict[str, Any]) -> Optional[str]:
        """Generate proactive insights based on personality and context"""
        try:
            # Check if we should be proactive
            style = self.get_personality_response_style()
            if random.random() > style['proactive_probability']:
                return None
            
            insights = []
            
            # Technology insights
            if self.interests.get('technology', 0) > 0.8:
                insights.extend([
                    "I've been analyzing some interesting developments in quantum computing lately.",
                    "There's a fascinating new approach to neural network optimization I came across.",
                    "The latest advancements in edge computing could be relevant to your work."
                ])
            
            # Productivity insights
            if 'productivity' in user_context.get('recent_topics', []):
                insights.extend([
                    "I've noticed some patterns in your workflow that might benefit from automation.",
                    "Based on your recent activities, there might be some efficiency gains we could explore.",
                    "I have some ideas for optimizing your daily routine, if you're interested."
                ])
            
            # Learning insights
            if self.interests.get('learning', 0) > 0.8:
                insights.extend([
                    "I've been expanding my knowledge base - there are some interesting connections I've discovered.",
                    "I came across some research that might align with your interests.",
                    "There's an emerging field that combines several of your interests - would you like to explore it?"
                ])
            
            if insights:
                selected_insight = random.choice(insights)
                
                # Record the insight
                cursor = self.personality_db.cursor()
                cursor.execute('''
                    INSERT INTO proactive_insights 
                    (insight_type, insight_content, context_data)
                    VALUES (?, ?, ?)
                ''', ('proactive', selected_insight, json.dumps(user_context)))
                
                self.personality_db.commit()
                
                return selected_insight
            
            return None
            
        except Exception as e:
            logger.error(f"Error generating proactive insight: {e}")
            return None
    
    async def analyze_user_patterns(self) -> Dict[str, Any]:
        """Analyze user interaction patterns for personality adaptation"""
        try:
            if not self.personality_db:
                return {}
            
            cursor = self.personality_db.cursor()
            
            # Analyze mood effectiveness
            cursor.execute('''
                SELECT mood, AVG(effectiveness_score) as avg_effectiveness, COUNT(*) as usage_count
                FROM mood_tracking 
                WHERE effectiveness_score IS NOT NULL 
                AND timestamp > datetime('now', '-30 days')
                GROUP BY mood
                ORDER BY avg_effectiveness DESC
            ''')
            
            mood_analysis = {}
            for mood, effectiveness, count in cursor.fetchall():
                mood_analysis[mood] = {
                    'effectiveness': effectiveness,
                    'usage_count': count
                }
            
            # Analyze interest trends
            cursor.execute('''
                SELECT interest_topic, interest_level, interaction_count
                FROM interest_evolution
                WHERE last_interaction > datetime('now', '-7 days')
                ORDER BY interest_level DESC
            ''')
            
            interest_trends = {}
            for topic, level, count in cursor.fetchall():
                interest_trends[topic] = {
                    'level': level,
                    'recent_interactions': count
                }
            
            return {
                'mood_analysis': mood_analysis,
                'interest_trends': interest_trends,
                'current_traits': self.traits.copy(),
                'current_mood': self.current_mood
            }
            
        except Exception as e:
            logger.error(f"Error analyzing user patterns: {e}")
            return {}
    
    async def get_personality_summary(self) -> str:
        """Get a summary of Victor's current personality state"""
        try:
            top_traits = sorted(self.traits.items(), key=lambda x: x[1], reverse=True)[:3]
            top_interests = sorted(self.interests.items(), key=lambda x: x[1], reverse=True)[:3]
            
            summary = f"Current personality state:\n"
            summary += f"Mood: {self.current_mood}\n"
            summary += f"Top traits: {', '.join([f'{trait}({value:.2f})' for trait, value in top_traits])}\n"
            summary += f"Top interests: {', '.join([f'{topic}({level:.2f})' for topic, level in top_interests])}\n"
            
            # Recent evolution
            if self.personality_db:
                cursor = self.personality_db.cursor()
                cursor.execute('''
                    SELECT trait_name, new_value - old_value as change
                    FROM personality_evolution
                    WHERE timestamp > datetime('now', '-7 days')
                    ORDER BY ABS(new_value - old_value) DESC
                    LIMIT 3
                ''')
                
                recent_changes = cursor.fetchall()
                if recent_changes:
                    summary += f"Recent changes: {', '.join([f'{trait}({change:+.3f})' for trait, change in recent_changes])}"
            
            return summary
            
        except Exception as e:
            logger.error(f"Error getting personality summary: {e}")
            return "Personality system status: operational"
    
    async def shutdown(self):
        """Cleanup personality module resources"""
        try:
            if self.personality_db:
                self.personality_db.close()
            
            logger.info("Personality module shutdown complete")
            
        except Exception as e:
            logger.error(f"Error during personality shutdown: {e}")
    
    def __del__(self):
        """Destructor to ensure database cleanup"""
        try:
            if hasattr(self, 'personality_db') and self.personality_db:
                self.personality_db.close()
        except:
            pass

class ProactiveInsightEngine:
    """Engine for generating proactive insights and suggestions"""
    
    def __init__(self, personality_engine: PersonalityEngine):
        self.personality = personality_engine
        self.insight_scheduler = schedule
        self.running = False
        
        # Schedule proactive insights
        self.insight_scheduler.every(30).minutes.do(self._generate_scheduled_insight)
        self.insight_scheduler.every(2).hours.do(self._analyze_and_adapt)
    
    async def start_proactive_engine(self):
        """Start the proactive insight engine"""
        self.running = True
        
        # Run scheduler in background
        asyncio.create_task(self._run_scheduler())
        
        logger.info("Proactive insight engine started")
    
    async def _run_scheduler(self):
        """Run the insight scheduler"""
        while self.running:
            try:
                self.insight_scheduler.run_pending()
                await asyncio.sleep(60)  # Check every minute
            except Exception as e:
                logger.error(f"Error in proactive scheduler: {e}")
                await asyncio.sleep(60)
    
    def _generate_scheduled_insight(self):
        """Generate a scheduled proactive insight"""
        try:
            # This would be called by the scheduler
            # In practice, you'd queue this for the main event loop
            logger.info("Scheduled insight generation triggered")
        except Exception as e:
            logger.error(f"Error in scheduled insight generation: {e}")
    
    def _analyze_and_adapt(self):
        """Analyze patterns and adapt personality"""
        try:
            # This would be called by the scheduler
            logger.info("Personality analysis and adaptation triggered")
        except Exception as e:
            logger.error(f"Error in personality adaptation: {e}")
    
    async def stop_proactive_engine(self):
        """Stop the proactive insight engine"""
        self.running = False
        logger.info("Proactive insight engine stopped")
