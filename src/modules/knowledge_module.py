"""
Intelligent Knowledge Base Module for <PERSON> 
Handles knowledge storage, retrieval, learning, and fact-checking
"""

import asyncio
import logging
import sqlite3
import json
import hashlib
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Tuple
from pathlib import Path
import aiohttp
import re

try:
    import openai
    OPENAI_AVAILABLE = True
except ImportError:
    OPENAI_AVAILABLE = False
    logging.warning("OpenAI not available")

try:
    from sklearn.feature_extraction.text import TfidfVectorizer
    from sklearn.metrics.pairwise import cosine_similarity
    import numpy as np
    SKLEARN_AVAILABLE = True
except ImportError:
    SKLEARN_AVAILABLE = False
    logging.warning("scikit-learn not available")

logger = logging.getLogger(__name__)

class IntelligentKnowledgeBase:
    """Intelligent knowledge base with learning and reasoning capabilities"""
    
    def __init__(self, config):
        self.config = config
        self.knowledge_config = config.knowledge
        
        # Database connection
        self.db_path = self.knowledge_config.database_path
        self.conn = None
        
        # Knowledge cache
        self.knowledge_cache = {}
        self.cache_timestamps = {}
        
        # Learning components
        self.vectorizer = None
        self.knowledge_vectors = None
        self.knowledge_texts = []
        
        # Web search session
        self.session = None
        
        # Initialize components
        self._initialize_database()
        self._initialize_learning_components()
        self._load_knowledge_cache()
    
    def _initialize_database(self):
        """Initialize SQLite database for knowledge storage"""
        try:
            # Ensure database directory exists
            Path(self.db_path).parent.mkdir(parents=True, exist_ok=True)
            
            self.conn = sqlite3.connect(self.db_path, check_same_thread=False)
            self.conn.execute("PRAGMA foreign_keys = ON")
            
            # Create tables
            self._create_tables()
            
            logger.info(f"Knowledge database initialized: {self.db_path}")
            
        except Exception as e:
            logger.error(f"Failed to initialize knowledge database: {e}")
            raise
    
    def _create_tables(self):
        """Create database tables"""
        cursor = self.conn.cursor()
        
        # Knowledge entries table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS knowledge (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                topic TEXT NOT NULL,
                content TEXT NOT NULL,
                source TEXT,
                confidence REAL DEFAULT 0.5,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                access_count INTEGER DEFAULT 0,
                last_accessed TIMESTAMP,
                tags TEXT,
                category TEXT,
                verified BOOLEAN DEFAULT FALSE
            )
        ''')
        
        # Learning interactions table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS interactions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_input TEXT NOT NULL,
                system_response TEXT NOT NULL,
                feedback_score REAL,
                context TEXT,
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                intent TEXT,
                entities TEXT
            )
        ''')
        
        # Facts and relationships table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS facts (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                subject TEXT NOT NULL,
                predicate TEXT NOT NULL,
                object TEXT NOT NULL,
                confidence REAL DEFAULT 0.5,
                source TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                verified BOOLEAN DEFAULT FALSE
            )
        ''')
        
        # User preferences and learning data
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS user_data (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id TEXT NOT NULL,
                data_type TEXT NOT NULL,
                data_value TEXT NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # Create indexes for better performance
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_knowledge_topic ON knowledge(topic)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_knowledge_category ON knowledge(category)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_facts_subject ON facts(subject)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_interactions_intent ON interactions(intent)')
        
        self.conn.commit()
        logger.info("Knowledge database tables created/verified")
    
    def _initialize_learning_components(self):
        """Initialize machine learning components for knowledge processing"""
        try:
            if SKLEARN_AVAILABLE:
                self.vectorizer = TfidfVectorizer(
                    max_features=1000,
                    stop_words='english',
                    ngram_range=(1, 2)
                )
                logger.info("Learning components initialized")
            else:
                logger.warning("scikit-learn not available -  learning disabled")
                
        except Exception as e:
            logger.error(f"Failed to initialize learning components: {e}")
    
    def _load_knowledge_cache(self):
        """Load frequently accessed knowledge into cache"""
        try:
            cursor = self.conn.cursor()
            cursor.execute('''
                SELECT topic, content, confidence 
                FROM knowledge 
                WHERE access_count > 5 
                ORDER BY access_count DESC 
                LIMIT ?
            ''', (self.knowledge_config.cache_size,))
            
            for topic, content, confidence in cursor.fetchall():
                self.knowledge_cache[topic] = {
                    'content': content,
                    'confidence': confidence
                }
                self.cache_timestamps[topic] = datetime.now()
            
            logger.info(f"Loaded {len(self.knowledge_cache)} items into knowledge cache")
            
        except Exception as e:
            logger.error(f"Failed to load knowledge cache: {e}")
    
    async def query_knowledge(self, analysis: Dict[str, Any]) -> str:
        """
        Query knowledge base based on analysis
        
        Args:
            analysis: NLP analysis of user query
            
        Returns:
            str: Knowledge response
        """
        try:
            query = analysis.get('original_text', '')
            keywords = analysis.get('keywords', [])
            entities = analysis.get('entities', [])
            
            # Try cache first
            cached_result = self._check_cache(query, keywords)
            if cached_result:
                return cached_result
            
            # Search local knowledge base
            local_result = await self._search_local_knowledge(query, keywords, entities)
            if local_result and local_result['confidence'] >= self.knowledge_config.confidence_threshold:
                return local_result['content']
            
            # Search web if enabled
            if self.knowledge_config.enable_web_search:
                web_result = await self._search_web_knowledge(query)
                if web_result:
                    # Store new knowledge
                    await self._store_knowledge(
                        topic=query,
                        content=web_result,
                        source='web_search',
                        confidence=0.7
                    )
                    return web_result
            
            # Generate response using AI if available
            if OPENAI_AVAILABLE and self.config.api.openai_api_key:
                ai_result = await self._generate_ai_knowledge(query)
                if ai_result:
                    await self._store_knowledge(
                        topic=query,
                        content=ai_result,
                        source='ai_generation',
                        confidence=0.6
                    )
                    return ai_result
            
            return "I don't have enough information about that topic. Could you provide more context or ask about something else?"
            
        except Exception as e:
            logger.error(f"Error querying knowledge: {e}")
            return f"I encountered an error while searching for information: {str(e)}"
    
    def _check_cache(self, query: str, keywords: List[str]) -> Optional[str]:
        """Check if query can be answered from cache"""
        try:
            query_lower = query.lower()
            
            # Direct topic match
            if query_lower in self.knowledge_cache:
                return self.knowledge_cache[query_lower]['content']
            
            # Keyword-based matching
            for topic, data in self.knowledge_cache.items():
                if any(keyword.lower() in topic.lower() for keyword in keywords):
                    return data['content']
            
            return None
            
        except Exception as e:
            logger.error(f"Error checking cache: {e}")
            return None
    
    async def _search_local_knowledge(self, query: str, keywords: List[str], entities: List[Dict]) -> Optional[Dict[str, Any]]:
        """Search local knowledge base"""
        try:
            cursor = self.conn.cursor()
            
            # Build search query
            search_terms = [query] + keywords
            entity_values = [entity.get('text', '') for entity in entities]
            all_terms = search_terms + entity_values
            
            # Search by topic and content
            search_pattern = '%' + '%'.join(all_terms[:3]) + '%'  # Limit to avoid too complex queries
            
            cursor.execute('''
                SELECT topic, content, confidence, access_count
                FROM knowledge
                WHERE (topic LIKE ? OR content LIKE ?)
                ORDER BY confidence DESC, access_count DESC
                LIMIT 5
            ''', (search_pattern, search_pattern))
            
            results = cursor.fetchall()
            
            if results:
                # Update access count for best match
                best_match = results[0]
                cursor.execute('''
                    UPDATE knowledge 
                    SET access_count = access_count + 1, last_accessed = CURRENT_TIMESTAMP
                    WHERE topic = ? AND content = ?
                ''', (best_match[0], best_match[1]))
                self.conn.commit()
                
                return {
                    'topic': best_match[0],
                    'content': best_match[1],
                    'confidence': best_match[2]
                }
            
            return None
            
        except Exception as e:
            logger.error(f"Error searching local knowledge: {e}")
            return None
    
    async def _search_web_knowledge(self, query: str) -> Optional[str]:
        """Search web for knowledge (placeholder implementation)"""
        try:
            # This is a simplified implementation
            # In practice, you'd integrate with search APIs like Google Custom Search
            
            if not self.session:
                self.session = aiohttp.ClientSession()
            
            # Example: Wikipedia API search
            wikipedia_url = "https://en.wikipedia.org/api/rest_v1/page/summary/"
            search_query = query.replace(' ', '_')
            
            try:
                async with self.session.get(f"{wikipedia_url}{search_query}") as response:
                    if response.status == 200:
                        data = await response.json()
                        extract = data.get('extract', '')
                        if extract and len(extract) > 50:
                            return f"According to Wikipedia: {extract}"
            except:
                pass
            
            return None
            
        except Exception as e:
            logger.error(f"Error in web search: {e}")
            return None
    
    async def _generate_ai_knowledge(self, query: str) -> Optional[str]:
        """Generate knowledge using AI"""
        try:
            if not OPENAI_AVAILABLE or not self.config.api.openai_api_key:
                return None
            
            prompt = f"Provide accurate, factual information about: {query}"
            
            response = await asyncio.to_thread(
                openai.Completion.create,
                engine="text-davinci-003",
                prompt=prompt,
                max_tokens=200,
                temperature=0.3
            )
            
            return response.choices[0].text.strip()
            
        except Exception as e:
            logger.error(f"Error generating AI knowledge: {e}")
            return None
    
    async def _store_knowledge(self, topic: str, content: str, source: str = 'user', confidence: float = 0.5, category: str = None, tags: List[str] = None):
        """Store new knowledge in the database"""
        try:
            cursor = self.conn.cursor()
            
            # Check if knowledge already exists
            cursor.execute('SELECT id FROM knowledge WHERE topic = ? AND content = ?', (topic, content))
            if cursor.fetchone():
                return  # Already exists
            
            # Insert new knowledge
            tags_str = json.dumps(tags) if tags else None
            
            cursor.execute('''
                INSERT INTO knowledge (topic, content, source, confidence, category, tags)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', (topic, content, source, confidence, category, tags_str))
            
            self.conn.commit()
            
            # Update cache if high confidence
            if confidence >= 0.7:
                self.knowledge_cache[topic] = {
                    'content': content,
                    'confidence': confidence
                }
                self.cache_timestamps[topic] = datetime.now()
            
            logger.info(f"Stored new knowledge: {topic[:50]}...")
            
        except Exception as e:
            logger.error(f"Error storing knowledge: {e}")
    
    async def learn_from_interaction(self, user_input: str, system_response: str, analysis: Dict[str, Any]):
        """Learn from user interactions to improve responses"""
        try:
            if not self.knowledge_config.enable_online_learning:
                return
            
            cursor = self.conn.cursor()
            
            # Store interaction
            cursor.execute('''
                INSERT INTO interactions (user_input, system_response, context, intent, entities)
                VALUES (?, ?, ?, ?, ?)
            ''', (
                user_input,
                system_response,
                json.dumps(analysis),
                analysis.get('intent', 'unknown'),
                json.dumps(analysis.get('entities', []))
            ))
            
            self.conn.commit()
            
            # Extract potential facts from interaction
            await self._extract_facts_from_interaction(user_input, system_response, analysis)
            
            # Update knowledge vectors if sklearn available
            if SKLEARN_AVAILABLE and self.vectorizer:
                await self._update_knowledge_vectors()
            
        except Exception as e:
            logger.error(f"Error learning from interaction: {e}")
    
    async def _extract_facts_from_interaction(self, user_input: str, system_response: str, analysis: Dict[str, Any]):
        """Extract facts from user interactions"""
        try:
            # Simple fact extraction based on patterns
            entities = analysis.get('entities', [])
            
            # Look for "X is Y" patterns
            is_pattern = r'(.+?)\s+is\s+(.+?)(?:\.|$)'
            matches = re.findall(is_pattern, user_input, re.IGNORECASE)
            
            for subject, predicate_object in matches:
                subject = subject.strip()
                predicate_object = predicate_object.strip()
                
                # Store as fact
                cursor = self.conn.cursor()
                cursor.execute('''
                    INSERT OR IGNORE INTO facts (subject, predicate, object, source, confidence)
                    VALUES (?, ?, ?, ?, ?)
                ''', (subject, 'is', predicate_object, 'user_interaction', 0.6))
                
                self.conn.commit()
            
        except Exception as e:
            logger.error(f"Error extracting facts: {e}")
    
    async def _update_knowledge_vectors(self):
        """Update knowledge vectors for similarity search"""
        try:
            if not SKLEARN_AVAILABLE:
                return
            
            cursor = self.conn.cursor()
            cursor.execute('SELECT topic, content FROM knowledge ORDER BY confidence DESC LIMIT 1000')
            
            texts = []
            for topic, content in cursor.fetchall():
                texts.append(f"{topic} {content}")
            
            if texts:
                self.knowledge_vectors = self.vectorizer.fit_transform(texts)
                self.knowledge_texts = texts
                logger.debug("Updated knowledge vectors")
            
        except Exception as e:
            logger.error(f"Error updating knowledge vectors: {e}")
    
    async def find_similar_knowledge(self, query: str, limit: int = 5) -> List[Dict[str, Any]]:
        """Find similar knowledge using vector similarity"""
        try:
            if not SKLEARN_AVAILABLE or self.knowledge_vectors is None:
                return []
            
            # Vectorize query
            query_vector = self.vectorizer.transform([query])
            
            # Calculate similarities
            similarities = cosine_similarity(query_vector, self.knowledge_vectors)[0]
            
            # Get top similar items
            top_indices = np.argsort(similarities)[::-1][:limit]
            
            results = []
            for idx in top_indices:
                if similarities[idx] > 0.1:  # Minimum similarity threshold
                    results.append({
                        'text': self.knowledge_texts[idx],
                        'similarity': float(similarities[idx])
                    })
            
            return results
            
        except Exception as e:
            logger.error(f"Error finding similar knowledge: {e}")
            return []
    
    async def get_knowledge_stats(self) -> Dict[str, Any]:
        """Get knowledge base statistics"""
        try:
            cursor = self.conn.cursor()
            
            # Count total knowledge entries
            cursor.execute('SELECT COUNT(*) FROM knowledge')
            total_knowledge = cursor.fetchone()[0]
            
            # Count interactions
            cursor.execute('SELECT COUNT(*) FROM interactions')
            total_interactions = cursor.fetchone()[0]
            
            # Count facts
            cursor.execute('SELECT COUNT(*) FROM facts')
            total_facts = cursor.fetchone()[0]
            
            # Get top categories
            cursor.execute('''
                SELECT category, COUNT(*) as count 
                FROM knowledge 
                WHERE category IS NOT NULL 
                GROUP BY category 
                ORDER BY count DESC 
                LIMIT 5
            ''')
            top_categories = cursor.fetchall()
            
            return {
                'total_knowledge': total_knowledge,
                'total_interactions': total_interactions,
                'total_facts': total_facts,
                'cache_size': len(self.knowledge_cache),
                'top_categories': top_categories
            }
            
        except Exception as e:
            logger.error(f"Error getting knowledge stats: {e}")
            return {}
    
    async def cleanup_old_knowledge(self, days: int = 30):
        """Clean up old, low-confidence knowledge"""
        try:
            cursor = self.conn.cursor()
            cutoff_date = datetime.now() - timedelta(days=days)
            
            # Remove low-confidence, rarely accessed knowledge
            cursor.execute('''
                DELETE FROM knowledge 
                WHERE confidence < 0.3 
                AND access_count < 2 
                AND created_at < ?
            ''', (cutoff_date,))
            
            deleted_count = cursor.rowcount
            self.conn.commit()
            
            logger.info(f"Cleaned up {deleted_count} old knowledge entries")
            
        except Exception as e:
            logger.error(f"Error cleaning up knowledge: {e}")
    
    async def shutdown(self):
        """Cleanup knowledge module resources"""
        try:
            if self.session:
                await self.session.close()
            
            if self.conn:
                self.conn.close()
            
            logger.info("Knowledge module shutdown complete")
            
        except Exception as e:
            logger.error(f"Error during knowledge shutdown: {e}")
    
    def __del__(self):
        """Destructor to ensure database cleanup"""
        try:
            if hasattr(self, 'conn') and self.conn:
                self.conn.close()
        except:
            pass
