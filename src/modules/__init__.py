"""
Victor  Modules Package
Contains all the specialized modules for Victor  Personal Assistant
"""

__version__ = "2.0.0"
__author__ = "Victor  Development Team"

# Module imports for easy access
from .audio_module import AudioProcessor
from .vision_module import VisionSystem
from .nlp_module import NLPProcessor
from .ollama_nlp_module import OllamaNLPProcessor
from .personality_module import Personality<PERSON>ngine, ProactiveInsightEngine
from .system_module import SystemCommandModule
from .knowledge_module import IntelligentKnowledgeBase
from .security_module import SecurityManager

__all__ = [
    'AudioProcessor',
    'VisionSystem',
    'NLPProcessor',
    'OllamaNLPProcessor',
    'PersonalityEngine',
    'ProactiveInsightEngine',
    'SystemCommandModule',
    'IntelligentKnowledgeBase',
    'SecurityManager'
]
