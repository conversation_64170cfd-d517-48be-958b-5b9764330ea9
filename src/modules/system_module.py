"""
System Command Module for Victor 
Handles system operations, file management, process control, and network operations
"""

import asyncio
import logging
import os
import sys
import subprocess
import shutil
import psutil
import platform
from pathlib import Path
from typing import Dict, Any, List, Optional, Union
import json
import time
from datetime import datetime

logger = logging.getLogger(__name__)

class SystemCommandModule:
    """ system command processing with safety features"""

    def __init__(self, config):
        self.config = config
        self.system_config = config.system
        self.platform = platform.system().lower()

        # Command mappings for different platforms
        self.command_mappings = {
            'windows': {
                'shutdown': 'shutdown /s /t 1',
                'restart': 'shutdown /r /t 1',
                'logout': 'shutdown /l',
                'volume_up': 'nircmd.exe changesysvolume 5000',
                'volume_down': 'nircmd.exe changesysvolume -5000',
                'volume_mute': 'nircmd.exe mutesysvolume 1',
                'volume_unmute': 'nircmd.exe mutesysvolume 0',
                'lock_screen': 'rundll32.exe user32.dll,LockWorkStation',
                'sleep': 'rundll32.exe powrprof.dll,SetSuspendState 0,1,0'
            },
            'linux': {
                'shutdown': 'sudo shutdown -h now',
                'restart': 'sudo reboot',
                'logout': 'pkill -KILL -u $USER',
                'volume_up': 'amixer -D pulse sset Master 5%+',
                'volume_down': 'amixer -D pulse sset Master 5%-',
                'volume_mute': 'amixer -D pulse sset Master mute',
                'volume_unmute': 'amixer -D pulse sset Master unmute',
                'lock_screen': 'gnome-screensaver-command -l',
                'sleep': 'systemctl suspend'
            },
            'darwin': {  # macOS
                'shutdown': 'sudo shutdown -h now',
                'restart': 'sudo reboot',
                'logout': 'osascript -e "tell application \\"System Events\\" to log out"',
                'volume_up': 'osascript -e "set volume output volume (output volume of (get volume settings) + 10)"',
                'volume_down': 'osascript -e "set volume output volume (output volume of (get volume settings) - 10)"',
                'volume_mute': 'osascript -e "set volume with output muted"',
                'volume_unmute': 'osascript -e "set volume without output muted"',
                'lock_screen': '/System/Library/CoreServices/Menu\\ Extras/User.menu/Contents/Resources/CGSession -suspend',
                'sleep': 'pmset sleepnow'
            }
        }

        logger.info(f"System module initialized for {self.platform}")

    async def execute_command(self, analysis: Dict[str, Any], context: Optional[Dict] = None) -> str:
        """
        Execute system command based on analysis

        Args:
            analysis: Command analysis from NLP module
            context: Additional context information

        Returns:
            str: Result message
        """
        try:
            command_type = self._identify_command_type(analysis)

            if not self._is_command_allowed(command_type, analysis):
                return "I'm sorry, but that command is not allowed in the current security mode."

            if command_type == 'power_management':
                return await self._handle_power_management(analysis)
            elif command_type == 'volume_control':
                return await self._handle_volume_control(analysis)
            elif command_type == 'process_management':
                return await self._handle_process_management(analysis)
            elif command_type == 'file_operation':
                return await self.handle_file_operation(analysis)
            elif command_type == 'system_info':
                return await self._handle_system_info(analysis)
            elif command_type == 'network_operation':
                return await self._handle_network_operation(analysis)
            else:
                return await self._handle_custom_command(analysis)

        except Exception as e:
            logger.error(f"Error executing system command: {e}")
            return f"An error occurred while executing the command: {str(e)}"

    def _identify_command_type(self, analysis: Dict[str, Any]) -> str:
        """Identify the type of system command"""
        text = analysis.get('original_text', '').lower()

        # Power management
        if any(word in text for word in ['shutdown', 'restart', 'reboot', 'sleep', 'hibernate', 'lock']):
            return 'power_management'

        # Volume control
        if any(word in text for word in ['volume', 'sound', 'audio', 'mute', 'unmute']):
            return 'volume_control'

        # Process management
        if any(word in text for word in ['process', 'task', 'kill', 'start', 'stop', 'application']):
            return 'process_management'

        # File operations
        if any(word in text for word in ['file', 'folder', 'directory', 'copy', 'move', 'delete', 'create']):
            return 'file_operation'

        # System information
        if any(word in text for word in ['system', 'info', 'status', 'memory', 'cpu', 'disk', 'performance']):
            return 'system_info'

        # Network operations
        if any(word in text for word in ['network', 'wifi', 'internet', 'connection', 'ping', 'ip']):
            return 'network_operation'

        return 'custom_command'

    def _is_command_allowed(self, command_type: str, analysis: Dict[str, Any]) -> bool:
        """Check if command is allowed based on security settings"""
        if not self.system_config.enable_system_commands:
            return False

        text = analysis.get('original_text', '').lower()

        # Check blocked commands
        for blocked in self.system_config.blocked_commands:
            if blocked.lower() in text:
                return False

        # In safe mode, restrict dangerous operations
        if self.system_config.safe_mode:
            dangerous_operations = ['shutdown', 'restart', 'format', 'delete', 'rm -rf']
            if any(op in text for op in dangerous_operations):
                return False

        return True

    async def _handle_power_management(self, analysis: Dict[str, Any]) -> str:
        """Handle power management commands"""
        text = analysis.get('original_text', '').lower()

        try:
            if 'shutdown' in text:
                if self.system_config.safe_mode:
                    return "Shutdown is disabled in safe mode. Please confirm if you want to proceed."
                command = self.command_mappings[self.platform]['shutdown']
                await self._execute_system_command(command)
                return "System is shutting down..."

            elif 'restart' in text or 'reboot' in text:
                if self.system_config.safe_mode:
                    return "Restart is disabled in safe mode. Please confirm if you want to proceed."
                command = self.command_mappings[self.platform]['restart']
                await self._execute_system_command(command)
                return "System is restarting..."

            elif 'logout' in text or 'log out' in text:
                command = self.command_mappings[self.platform]['logout']
                await self._execute_system_command(command)
                return "Logging out..."

            elif 'lock' in text:
                command = self.command_mappings[self.platform]['lock_screen']
                await self._execute_system_command(command)
                return "Screen locked."

            elif 'sleep' in text or 'suspend' in text:
                command = self.command_mappings[self.platform]['sleep']
                await self._execute_system_command(command)
                return "System going to sleep..."

            else:
                return "I didn't understand the power management command."

        except Exception as e:
            logger.error(f"Power management error: {e}")
            return f"Failed to execute power management command: {str(e)}"

    async def _handle_volume_control(self, analysis: Dict[str, Any]) -> str:
        """Handle volume control commands"""
        text = analysis.get('original_text', '').lower()

        try:
            if 'up' in text or 'increase' in text or 'louder' in text:
                command = self.command_mappings[self.platform]['volume_up']
                await self._execute_system_command(command)
                return "Volume increased."

            elif 'down' in text or 'decrease' in text or 'lower' in text or 'quieter' in text:
                command = self.command_mappings[self.platform]['volume_down']
                await self._execute_system_command(command)
                return "Volume decreased."

            elif 'mute' in text:
                command = self.command_mappings[self.platform]['volume_mute']
                await self._execute_system_command(command)
                return "Audio muted."

            elif 'unmute' in text:
                command = self.command_mappings[self.platform]['volume_unmute']
                await self._execute_system_command(command)
                return "Audio unmuted."

            else:
                return "I didn't understand the volume command."

        except Exception as e:
            logger.error(f"Volume control error: {e}")
            return f"Failed to control volume: {str(e)}"

    async def _handle_process_management(self, analysis: Dict[str, Any]) -> str:
        """Handle process management commands"""
        text = analysis.get('original_text', '').lower()

        try:
            if 'list' in text or 'show' in text:
                processes = await self._get_running_processes()
                return f"Running processes: {', '.join(processes[:10])}..."  # Show first 10

            elif 'kill' in text or 'stop' in text or 'terminate' in text:
                # Extract process name from entities
                process_name = self._extract_process_name(analysis)
                if process_name:
                    result = await self._kill_process(process_name)
                    return result
                else:
                    return "Please specify which process to terminate."

            elif 'start' in text or 'launch' in text or 'open' in text:
                # Extract application name
                app_name = self._extract_app_name(analysis)
                if app_name:
                    result = await self._start_application(app_name)
                    return result
                else:
                    return "Please specify which application to start."

            else:
                return "I didn't understand the process management command."

        except Exception as e:
            logger.error(f"Process management error: {e}")
            return f"Failed to manage process: {str(e)}"

    async def handle_file_operation(self, analysis: Dict[str, Any]) -> str:
        """Handle file operations"""
        text = analysis.get('original_text', '').lower()

        try:
            if not self.system_config.enable_file_operations:
                return "File operations are disabled."

            if 'create' in text:
                return await self._create_file_or_folder(analysis)
            elif 'delete' in text or 'remove' in text:
                return await self._delete_file_or_folder(analysis)
            elif 'copy' in text:
                return await self._copy_file_or_folder(analysis)
            elif 'move' in text:
                return await self._move_file_or_folder(analysis)
            elif 'list' in text or 'show' in text:
                return await self._list_directory(analysis)
            else:
                return "I didn't understand the file operation."

        except Exception as e:
            logger.error(f"File operation error: {e}")
            return f"Failed to perform file operation: {str(e)}"

    async def _handle_system_info(self, analysis: Dict[str, Any]) -> str:
        """Handle system information requests"""
        text = analysis.get('original_text', '').lower()

        try:
            if 'memory' in text or 'ram' in text:
                memory = psutil.virtual_memory()
                return f"Memory usage: {memory.percent}% ({memory.used // (1024**3)}GB / {memory.total // (1024**3)}GB)"

            elif 'cpu' in text or 'processor' in text:
                cpu_percent = psutil.cpu_percent(interval=1)
                cpu_count = psutil.cpu_count()
                return f"CPU usage: {cpu_percent}% ({cpu_count} cores)"

            elif 'disk' in text or 'storage' in text:
                disk = psutil.disk_usage('/')
                return f"Disk usage: {disk.percent}% ({disk.used // (1024**3)}GB / {disk.total // (1024**3)}GB)"

            elif 'temperature' in text:
                try:
                    temps = psutil.sensors_temperatures()
                    if temps:
                        temp_info = []
                        for name, entries in temps.items():
                            for entry in entries:
                                temp_info.append(f"{name}: {entry.current}°C")
                        return f"System temperatures: {', '.join(temp_info[:3])}"
                    else:
                        return "Temperature sensors not available."
                except:
                    return "Temperature information not available."

            elif 'uptime' in text:
                boot_time = psutil.boot_time()
                uptime = time.time() - boot_time
                hours = int(uptime // 3600)
                minutes = int((uptime % 3600) // 60)
                return f"System uptime: {hours} hours, {minutes} minutes"

            else:
                # General system info
                return await self._get_general_system_info()

        except Exception as e:
            logger.error(f"System info error: {e}")
            return f"Failed to get system information: {str(e)}"

    async def _handle_network_operation(self, analysis: Dict[str, Any]) -> str:
        """Handle network operations"""
        text = analysis.get('original_text', '').lower()

        try:
            if not self.system_config.enable_network_operations:
                return "Network operations are disabled."

            if 'status' in text or 'connection' in text:
                return await self._get_network_status()
            elif 'ping' in text:
                target = self._extract_ping_target(analysis)
                return await self._ping_host(target)
            elif 'ip' in text:
                return await self._get_ip_info()
            else:
                return "I didn't understand the network command."

        except Exception as e:
            logger.error(f"Network operation error: {e}")
            return f"Failed to perform network operation: {str(e)}"

    async def _handle_custom_command(self, analysis: Dict[str, Any]) -> str:
        """Handle custom system commands"""
        text = analysis.get('original_text', '')

        # This is a placeholder for custom command handling
        # In a real implementation, you'd want to be very careful about
        # executing arbitrary commands for security reasons

        return f"Custom command handling not implemented for: {text}"

    async def _execute_system_command(self, command: str) -> str:
        """Execute a system command safely"""
        try:
            if self.platform == 'windows':
                result = await asyncio.create_subprocess_shell(
                    command,
                    stdout=asyncio.subprocess.PIPE,
                    stderr=asyncio.subprocess.PIPE
                )
            else:
                result = await asyncio.create_subprocess_shell(
                    command,
                    stdout=asyncio.subprocess.PIPE,
                    stderr=asyncio.subprocess.PIPE
                )

            stdout, stderr = await result.communicate()

            if result.returncode == 0:
                return stdout.decode().strip() if stdout else "Command executed successfully."
            else:
                error_msg = stderr.decode().strip() if stderr else "Command failed."
                logger.error(f"Command failed: {command}, Error: {error_msg}")
                return f"Command failed: {error_msg}"

        except Exception as e:
            logger.error(f"Error executing command '{command}': {e}")
            return f"Failed to execute command: {str(e)}"

    async def _get_running_processes(self) -> List[str]:
        """Get list of running processes"""
        try:
            processes = []
            for proc in psutil.process_iter(['pid', 'name']):
                try:
                    processes.append(proc.info['name'])
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    pass
            return list(set(processes))  # Remove duplicates
        except Exception as e:
            logger.error(f"Error getting processes: {e}")
            return []

    def _extract_process_name(self, analysis: Dict[str, Any]) -> Optional[str]:
        """Extract process name from analysis"""
        # This would be implemented based on your NLP analysis structure
        entities = analysis.get('entities', [])
        for entity in entities:
            if entity.get('type') == 'process' or entity.get('type') == 'application':
                return entity.get('value')
        return None

    def _extract_app_name(self, analysis: Dict[str, Any]) -> Optional[str]:
        """Extract application name from analysis"""
        # Similar to process name extraction
        return self._extract_process_name(analysis)

    async def _kill_process(self, process_name: str) -> str:
        """Kill a process by name"""
        try:
            killed_count = 0
            for proc in psutil.process_iter(['pid', 'name']):
                try:
                    if proc.info['name'].lower() == process_name.lower():
                        proc.terminate()
                        killed_count += 1
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    pass

            if killed_count > 0:
                return f"Terminated {killed_count} instance(s) of {process_name}."
            else:
                return f"No running process found with name: {process_name}"

        except Exception as e:
            logger.error(f"Error killing process {process_name}: {e}")
            return f"Failed to terminate process: {str(e)}"

    async def _start_application(self, app_name: str) -> str:
        """Start an application"""
        try:
            if self.platform == 'windows':
                subprocess.Popen(['start', app_name], shell=True)
            elif self.platform == 'darwin':
                subprocess.Popen(['open', '-a', app_name])
            else:  # Linux
                subprocess.Popen([app_name])

            return f"Started {app_name}."

        except Exception as e:
            logger.error(f"Error starting application {app_name}: {e}")
            return f"Failed to start {app_name}: {str(e)}"

    async def _create_file_or_folder(self, analysis: Dict[str, Any]) -> str:
        """Create a file or folder"""
        try:
            # Extract path from analysis
            path_str = self._extract_file_path(analysis)
            if not path_str:
                return "Please specify the file or folder path."

            path = Path(path_str)

            # Security check
            if not self._is_path_allowed(path):
                return "Access to that location is not allowed."

            if 'folder' in analysis.get('original_text', '').lower() or 'directory' in analysis.get('original_text', '').lower():
                path.mkdir(parents=True, exist_ok=True)
                return f"Created folder: {path}"
            else:
                path.parent.mkdir(parents=True, exist_ok=True)
                path.touch()
                return f"Created file: {path}"

        except Exception as e:
            logger.error(f"Error creating file/folder: {e}")
            return f"Failed to create file/folder: {str(e)}"

    async def _delete_file_or_folder(self, analysis: Dict[str, Any]) -> str:
        """Delete a file or folder"""
        try:
            path_str = self._extract_file_path(analysis)
            if not path_str:
                return "Please specify the file or folder path."

            path = Path(path_str)

            if not self._is_path_allowed(path):
                return "Access to that location is not allowed."

            if not path.exists():
                return f"Path does not exist: {path}"

            if path.is_file():
                path.unlink()
                return f"Deleted file: {path}"
            elif path.is_dir():
                shutil.rmtree(path)
                return f"Deleted folder: {path}"
            else:
                return f"Unknown path type: {path}"

        except Exception as e:
            logger.error(f"Error deleting file/folder: {e}")
            return f"Failed to delete file/folder: {str(e)}"

    async def _copy_file_or_folder(self, analysis: Dict[str, Any]) -> str:
        """Copy a file or folder"""
        try:
            # This would need more sophisticated parsing to extract source and destination
            return "Copy operation needs source and destination paths."
        except Exception as e:
            logger.error(f"Error copying: {e}")
            return f"Failed to copy: {str(e)}"

    async def _move_file_or_folder(self, analysis: Dict[str, Any]) -> str:
        """Move a file or folder"""
        try:
            # This would need more sophisticated parsing to extract source and destination
            return "Move operation needs source and destination paths."
        except Exception as e:
            logger.error(f"Error moving: {e}")
            return f"Failed to move: {str(e)}"

    async def _list_directory(self, analysis: Dict[str, Any]) -> str:
        """List directory contents"""
        try:
            path_str = self._extract_file_path(analysis) or str(Path.home())
            path = Path(path_str)

            if not self._is_path_allowed(path):
                return "Access to that location is not allowed."

            if not path.exists() or not path.is_dir():
                return f"Directory does not exist: {path}"

            items = []
            for item in path.iterdir():
                if item.is_file():
                    size = item.stat().st_size
                    items.append(f"📄 {item.name} ({size} bytes)")
                elif item.is_dir():
                    items.append(f"📁 {item.name}/")

            if not items:
                return f"Directory is empty: {path}"

            return f"Contents of {path}:\n" + "\n".join(items[:20])  # Limit to 20 items

        except Exception as e:
            logger.error(f"Error listing directory: {e}")
            return f"Failed to list directory: {str(e)}"

    async def _get_general_system_info(self) -> str:
        """Get general system information"""
        try:
            info = []
            info.append(f"Platform: {platform.platform()}")
            info.append(f"Architecture: {platform.architecture()[0]}")
            info.append(f"Processor: {platform.processor()}")

            memory = psutil.virtual_memory()
            info.append(f"Memory: {memory.percent}% used")

            cpu_percent = psutil.cpu_percent(interval=1)
            info.append(f"CPU: {cpu_percent}% usage")

            return "\n".join(info)

        except Exception as e:
            logger.error(f"Error getting system info: {e}")
            return f"Failed to get system information: {str(e)}"

    async def _get_network_status(self) -> str:
        """Get network connection status"""
        try:
            interfaces = psutil.net_if_addrs()
            stats = psutil.net_if_stats()

            active_interfaces = []
            for interface, addresses in interfaces.items():
                if interface in stats and stats[interface].isup:
                    for addr in addresses:
                        if addr.family == 2:  # IPv4
                            active_interfaces.append(f"{interface}: {addr.address}")

            if active_interfaces:
                return f"Active network interfaces:\n" + "\n".join(active_interfaces)
            else:
                return "No active network interfaces found."

        except Exception as e:
            logger.error(f"Error getting network status: {e}")
            return f"Failed to get network status: {str(e)}"

    async def _ping_host(self, target: str = "*******") -> str:
        """Ping a host"""
        try:
            if self.platform == 'windows':
                command = f"ping -n 4 {target}"
            else:
                command = f"ping -c 4 {target}"

            result = await self._execute_system_command(command)
            return f"Ping results for {target}:\n{result}"

        except Exception as e:
            logger.error(f"Error pinging {target}: {e}")
            return f"Failed to ping {target}: {str(e)}"

    async def _get_ip_info(self) -> str:
        """Get IP address information"""
        try:
            interfaces = psutil.net_if_addrs()
            ip_info = []

            for interface, addresses in interfaces.items():
                for addr in addresses:
                    if addr.family == 2:  # IPv4
                        ip_info.append(f"{interface}: {addr.address}")

            return "IP addresses:\n" + "\n".join(ip_info)

        except Exception as e:
            logger.error(f"Error getting IP info: {e}")
            return f"Failed to get IP information: {str(e)}"

    def _extract_file_path(self, analysis: Dict[str, Any]) -> Optional[str]:
        """Extract file path from analysis"""
        # This would be implemented based on your NLP analysis structure
        entities = analysis.get('entities', [])
        for entity in entities:
            if entity.get('type') == 'file_path' or entity.get('type') == 'path':
                return entity.get('value')

        # Fallback: try to extract from original text
        text = analysis.get('original_text', '')
        # Simple path extraction (would need more sophisticated implementation)
        words = text.split()
        for word in words:
            if '/' in word or '\\' in word or word.endswith('.txt') or word.endswith('.py'):
                return word

        return None

    def _extract_ping_target(self, analysis: Dict[str, Any]) -> str:
        """Extract ping target from analysis"""
        entities = analysis.get('entities', [])
        for entity in entities:
            if entity.get('type') == 'url' or entity.get('type') == 'ip':
                return entity.get('value')

        # Default to Google DNS
        return "*******"

    def _is_path_allowed(self, path: Path) -> bool:
        """Check if path is in allowed directories"""
        try:
            path = path.resolve()
            for allowed_dir in self.system_config.allowed_directories:
                allowed_path = Path(allowed_dir).resolve()
                if path.is_relative_to(allowed_path):
                    return True
            return False
        except Exception:
            return False

    async def shutdown(self):
        """Cleanup system module resources"""
        logger.info("System module shutdown complete")
