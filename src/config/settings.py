"""
Configuration management for <PERSON> 
"""

import json
import os
from pathlib import Path
from typing import Dict, Any, Optional, List
from dataclasses import dataclass, asdict
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

@dataclass
class AudioConfig:
    """Audio processing configuration"""
    wake_word_model_path: str = "Models/Victor_en_windows_v3_0_0.ppn"
    voice_profile: str = "en-US-Wavenet-D"
    sample_rate: int = 16000
    chunk_size: int = 1024
    channels: int = 1
    enable_noise_reduction: bool = True
    volume_threshold: float = 0.5
    timeout: float = 5.0
    phrase_timeout: float = 1.0

@dataclass
class VisionConfig:
    """Computer vision configuration"""
    camera_index: int = 0
    resolution: tuple = (640, 480)
    fps: int = 30
    face_detection_model: str = "haarcascade_frontalface_default.xml"
    object_detection_model: str = "yolov5x"
    pose_estimation_model: str = "yolov8n-pose.pt"
    confidence_threshold: float = 0.5
    enable_face_recognition: bool = True
    enable_gesture_recognition: bool = True

@dataclass
class NLPConfig:
    """Natural Language Processing configuration"""
    language_model: str = "gpt2-medium"
    spacy_model: str = "en_core_web_trf"
    sentiment_model: str = "j-hartmann/emotion-english-distilroberta-base"
    summarization_model: str = "facebook/bart-large-cnn"
    question_answering_model: str = "deepset/roberta-base-squad2"
    max_response_length: int = 150
    temperature: float = 0.7
    enable_context_awareness: bool = True

@dataclass
class SystemConfig:
    """System integration configuration"""
    enable_file_operations: bool = True
    enable_process_control: bool = True
    enable_network_operations: bool = True
    enable_system_commands: bool = True
    safe_mode: bool = True
    allowed_directories: Optional[List[str]] = None
    blocked_commands: Optional[List[str]] = None
    max_file_size: int = 100 * 1024 * 1024  # 100MB

    def __post_init__(self):
        if self.allowed_directories is None:
            self.allowed_directories = [
                str(Path.home()),
                str(Path.home() / "Documents"),
                str(Path.home() / "Downloads"),
                str(Path.home() / "Desktop")
            ]

        if self.blocked_commands is None:
            self.blocked_commands = [
                "rm -rf /",
                "format",
                "del /f /s /q",
                "shutdown -h now",
                "reboot"
            ]

@dataclass
class SecurityConfig:
    """Security and authentication configuration"""
    enable_authentication: bool = True
    authentication_method: str = "voice"  # voice, password, biometric
    max_failed_attempts: int = 3
    session_timeout: int = 3600  # seconds
    enable_encryption: bool = True
    log_security_events: bool = True
    require_confirmation_for_sensitive: bool = True
    sensitive_operations: Optional[List[str]] = None

    def __post_init__(self):
        if self.sensitive_operations is None:
            self.sensitive_operations = [
                "file_delete",
                "system_shutdown",
                "process_kill",
                "network_config",
                "user_management"
            ]

@dataclass
class KnowledgeConfig:
    """Knowledge base and learning configuration"""
    database_path: str = "data/victor_knowledge.db"
    enable_online_learning: bool = True
    enable_web_search: bool = True
    cache_size: int = 1000
    learning_rate: float = 0.01
    confidence_threshold: float = 0.8
    max_context_length: int = 4096
    enable_fact_checking: bool = True

@dataclass
class APIConfig:
    """External API configuration"""
    openai_api_key: str = ""
    google_cloud_key_path: str = ""
    weather_api_key: str = ""
    news_api_key: str = ""
    search_api_key: str = ""
    email_smtp_server: str = "smtp.gmail.com"
    email_smtp_port: int = 587

    def __post_init__(self):
        # Load from environment variables
        self.openai_api_key = os.getenv("OPENAI_API_KEY", self.openai_api_key)
        self.google_cloud_key_path = os.getenv("GOOGLE_APPLICATION_CREDENTIALS", self.google_cloud_key_path)
        self.weather_api_key = os.getenv("OPENWEATHER_API_KEY", self.weather_api_key)
        self.news_api_key = os.getenv("NEWS_API_KEY", self.news_api_key)
        self.search_api_key = os.getenv("SEARCH_API_KEY", self.search_api_key)

@dataclass
class LoggingConfig:
    """Logging configuration"""
    log_level: str = "INFO"
    log_file: str = "logs/victor.log"
    max_log_size: int = 10 * 1024 * 1024  # 10MB
    backup_count: int = 5
    enable_console_logging: bool = True
    enable_file_logging: bool = True
    log_format: str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"

class VictorConfig:
    """Main configuration class for Victor """

    def __init__(self):
        self.audio = AudioConfig()
        self.vision = VisionConfig()
        self.nlp = NLPConfig()
        self.system = SystemConfig()
        self.security = SecurityConfig()
        self.knowledge = KnowledgeConfig()
        self.api = APIConfig()
        self.logging = LoggingConfig()

        # General settings
        self.debug_mode = False
        self.performance_monitoring = True
        self.auto_save_interval = 300  # seconds
        self.max_memory_usage = 2 * 1024 * 1024 * 1024  # 2GB

    def to_dict(self) -> Dict[str, Any]:
        """Convert configuration to dictionary"""
        return {
            'audio': asdict(self.audio),
            'vision': asdict(self.vision),
            'nlp': asdict(self.nlp),
            'system': asdict(self.system),
            'security': asdict(self.security),
            'knowledge': asdict(self.knowledge),
            'api': asdict(self.api),
            'logging': asdict(self.logging),
            'debug_mode': self.debug_mode,
            'performance_monitoring': self.performance_monitoring,
            'auto_save_interval': self.auto_save_interval,
            'max_memory_usage': self.max_memory_usage
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'VictorConfig':
        """Create configuration from dictionary"""
        config = cls()

        if 'audio' in data:
            config.audio = AudioConfig(**data['audio'])
        if 'vision' in data:
            config.vision = VisionConfig(**data['vision'])
        if 'nlp' in data:
            config.nlp = NLPConfig(**data['nlp'])
        if 'system' in data:
            config.system = SystemConfig(**data['system'])
        if 'security' in data:
            config.security = SecurityConfig(**data['security'])
        if 'knowledge' in data:
            config.knowledge = KnowledgeConfig(**data['knowledge'])
        if 'api' in data:
            config.api = APIConfig(**data['api'])
        if 'logging' in data:
            config.logging = LoggingConfig(**data['logging'])

        # General settings
        config.debug_mode = data.get('debug_mode', False)
        config.performance_monitoring = data.get('performance_monitoring', True)
        config.auto_save_interval = data.get('auto_save_interval', 300)
        config.max_memory_usage = data.get('max_memory_usage', 2 * 1024 * 1024 * 1024)

        return config

    def save(self, config_path: str):
        """Save configuration to file"""
        config_file = Path(config_path)
        config_file.parent.mkdir(parents=True, exist_ok=True)

        with open(config_file, 'w') as f:
            json.dump(self.to_dict(), f, indent=2)

    @classmethod
    def load(cls, config_path: str) -> 'VictorConfig':
        """Load configuration from file"""
        config_file = Path(config_path)

        if config_file.exists():
            try:
                with open(config_file, 'r') as f:
                    data = json.load(f)
                return cls.from_dict(data)
            except Exception as e:
                print(f"Warning: Failed to load config from {config_path}: {e}")
                print("Using default configuration")

        # Create default config and save it
        config = cls()
        config.save(config_path)
        return config

    def validate(self) -> bool:
        """Validate configuration settings"""
        errors = []

        # Validate paths
        if self.audio.wake_word_model_path and not Path(self.audio.wake_word_model_path).exists():
            errors.append(f"Wake word model not found: {self.audio.wake_word_model_path}")

        # Validate API keys
        if not self.api.openai_api_key:
            errors.append("OpenAI API key not configured")

        # Validate directories
        if self.system.allowed_directories:
            for directory in self.system.allowed_directories:
                if not Path(directory).exists():
                    errors.append(f"Allowed directory does not exist: {directory}")

        if errors:
            print("Configuration validation errors:")
            for error in errors:
                print(f"  - {error}")
            return False

        return True

# Create default configuration file if it doesn't exist
def create_default_config():
    """Create default configuration file"""
    config_dir = Path("config")
    config_dir.mkdir(exist_ok=True)

    config_file = config_dir / "victor_config.json"
    if not config_file.exists():
        config = VictorConfig()
        config.save(str(config_file))
        print(f"Created default configuration at {config_file}")

if __name__ == "__main__":
    create_default_config()
