#!/usr/bin/env python3
"""
Victor  - Basic Usage Examples
Demonstrates how to use <PERSON>  programmatically
"""

import sys
import asyncio
import logging
from pathlib import Path

# Add parent directory to path
sys.path.insert(0, str(Path(__file__).parent.parent))

from victor_ import Victor

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def example_text_commands():
    """Example of processing text commands without audio"""
    print("=" * 60)
    print("Text Command Processing Examples")
    print("=" * 60)
    
    try:
        # Create Victor instance
        victor = Victor()
        
        # Example commands
        commands = [
            "what time is it",
            "tell me about artificial intelligence",
            "what's the weather like in New York",
            "list files in my home directory",
            "what do you see",
            "create a new folder called test_folder",
            "show me system information"
        ]
        
        for command in commands:
            print(f"\nUser: {command}")
            response = await victor.process_command(command)
            print(f"Victor: {response}")
            
            # Small delay between commands
            await asyncio.sleep(1)
        
        # Cleanup
        await victor.shutdown()
        
    except Exception as e:
        logger.error(f"Error in text command example: {e}")

async def example_module_usage():
    """Example of using individual modules"""
    print("\n" + "=" * 60)
    print("Individual Module Usage Examples")
    print("=" * 60)
    
    try:
        from config.settings import VictorConfig
        from modules.nlp_module import NLPProcessor
        from modules.system_module import SystemCommandModule
        
        # Load configuration
        config = VictorConfig()
        
        # NLP Module Example
        print("\n--- NLP Module Example ---")
        nlp = NLPProcessor(config)
        
        text = "Please shutdown the computer and create a new file"
        analysis = await nlp.analyze_command(text)
        
        print(f"Text: {text}")
        print(f"Intent: {analysis['intent']}")
        print(f"Confidence: {analysis['confidence']:.2f}")
        print(f"Entities: {analysis['entities']}")
        
        # System Module Example
        print("\n--- System Module Example ---")
        system = SystemCommandModule(config)
        
        # Simulate a system info command
        system_analysis = {
            'original_text': 'show me system information',
            'intent': 'system_info',
            'entities': []
        }
        
        response = await system.execute_command(system_analysis)
        print(f"System Info Response: {response}")
        
        # Cleanup
        await nlp.shutdown()
        await system.shutdown()
        
    except Exception as e:
        logger.error(f"Error in module usage example: {e}")

async def example_knowledge_learning():
    """Example of knowledge base learning"""
    print("\n" + "=" * 60)
    print("Knowledge Base Learning Example")
    print("=" * 60)
    
    try:
        from config.settings import VictorConfig
        from modules.knowledge_module import IntelligentKnowledgeBase
        from modules.nlp_module import NLPProcessor
        
        config = VictorConfig()
        knowledge = IntelligentKnowledgeBase(config)
        nlp = NLPProcessor(config)
        
        # Store some knowledge
        await knowledge._store_knowledge(
            topic="Python programming",
            content="Python is a high-level programming language known for its simplicity and readability.",
            source="example",
            confidence=0.9
        )
        
        # Query the knowledge
        query_analysis = await nlp.analyze_command("tell me about Python programming")
        response = await knowledge.query_knowledge(query_analysis)
        
        print(f"Query: tell me about Python programming")
        print(f"Response: {response}")
        
        # Get knowledge stats
        stats = await knowledge.get_knowledge_stats()
        print(f"\nKnowledge Base Stats: {stats}")
        
        # Cleanup
        await knowledge.shutdown()
        await nlp.shutdown()
        
    except Exception as e:
        logger.error(f"Error in knowledge learning example: {e}")

async def example_security_features():
    """Example of security features"""
    print("\n" + "=" * 60)
    print("Security Features Example")
    print("=" * 60)
    
    try:
        from config.settings import VictorConfig
        from modules.security_module import SecurityManager
        
        config = VictorConfig()
        security = SecurityManager(config)
        
        # Test encryption
        sensitive_data = "This is sensitive information"
        encrypted = security.encrypt_data(sensitive_data)
        decrypted = security.decrypt_data(encrypted)
        
        print(f"Original: {sensitive_data}")
        print(f"Encrypted: {encrypted[:50]}...")
        print(f"Decrypted: {decrypted}")
        print(f"Encryption working: {sensitive_data == decrypted}")
        
        # Get security status
        status = await security._get_security_status()
        print(f"\nSecurity Status:\n{status}")
        
        # Cleanup
        await security.shutdown()
        
    except Exception as e:
        logger.error(f"Error in security example: {e}")

async def example_configuration():
    """Example of configuration management"""
    print("\n" + "=" * 60)
    print("Configuration Management Example")
    print("=" * 60)
    
    try:
        from config.settings import VictorConfig
        
        # Load default configuration
        config = VictorConfig()
        
        print("Current Configuration:")
        print(f"- Audio sample rate: {config.audio.sample_rate}")
        print(f"- Vision camera index: {config.vision.camera_index}")
        print(f"- NLP language model: {config.nlp.language_model}")
        print(f"- Security enabled: {config.security.enable_authentication}")
        print(f"- Safe mode: {config.system.safe_mode}")
        
        # Modify configuration
        config.audio.sample_rate = 22050
        config.system.safe_mode = False
        
        print("\nModified Configuration:")
        print(f"- Audio sample rate: {config.audio.sample_rate}")
        print(f"- Safe mode: {config.system.safe_mode}")
        
        # Save configuration
        config.save("examples/example_config.json")
        print("\nConfiguration saved to examples/example_config.json")
        
        # Load configuration
        loaded_config = VictorConfig.load("examples/example_config.json")
        print(f"Loaded sample rate: {loaded_config.audio.sample_rate}")
        
    except Exception as e:
        logger.error(f"Error in configuration example: {e}")

async def main():
    """Main example function"""
    print("Victor  - Usage Examples")
    print("This script demonstrates various ways to use Victor ")
    
    # Create examples directory
    Path("examples").mkdir(exist_ok=True)
    
    examples = [
        ("Text Command Processing", example_text_commands),
        ("Individual Module Usage", example_module_usage),
        ("Knowledge Base Learning", example_knowledge_learning),
        ("Security Features", example_security_features),
        ("Configuration Management", example_configuration)
    ]
    
    for name, example_func in examples:
        try:
            print(f"\n{'='*20} {name} {'='*20}")
            await example_func()
        except Exception as e:
            logger.error(f"Error in {name}: {e}")
        
        # Pause between examples
        await asyncio.sleep(2)
    
    print("\n" + "=" * 60)
    print("Examples completed!")
    print("=" * 60)

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\nExamples interrupted by user")
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        sys.exit(1)
