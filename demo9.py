import logging
import asyncio
import sqlite3
import os
import threading
import time
import datetime
import pyaudio
import cv2
import numpy as np
import requests
import smtplib
import sys
import webbrowser
import pyjokes
import torch
import torchaudio
import tempfile
from google.api_core.exceptions import InvalidArgument
from google.auth.exceptions import RefreshError
from functools import lru_cache
from dotenv import load_dotenv
from playsound import playsound
from transformers import pipeline, AutoTokenizer, AutoModelForCausalLM
from google.cloud import texttospeech as tts
from sklearn.svm import SVC
from sklearn.metrics import accuracy_score
from sklearn.ensemble import IsolationForest
from sklearn.linear_model import SGDClassifier
import pvporcupine
import speech_recognition as sr
import joblib
import spacy
import inspect
import json
from hashlib import sha256
from pathlib import Path
import random
import numpy as np

# Load environment variables
load_dotenv()

# Initialize logging
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(levelname)s - %(message)s')


class AssistantCore:
    def __init__(self):
        self.context = {
            "last_command": None,
            "user_info": {},
            "conversation_history": [],
            "humor_level": "normal"
        }
        self.nlp = spacy.load("en_core_web_trf")
        self.summarizer = pipeline("summarization", model="sshleifer/distilbart-cnn-12-6")
        self.question_answerer = pipeline("question-answering", model="distilbert-base-cased-distilled-squad")
        self.recognizer = sr.Recognizer()
        self.emotion_recognition = pipeline("sentiment-analysis", model="j-hartmann/emotion-english-distilroberta-base")
        self.audio_emotion_model = pipeline("audio-classification", model="MIT/ast-finetuned-audioset-10-10-0.4593")
        self.client = tts.TextToSpeechClient()
        self.tokenizer = AutoTokenizer.from_pretrained("gpt2")
        self.model = AutoModelForCausalLM.from_pretrained("gpt2")
        self.database = DatabaseModule()
        self.feedback_mechanism = FeedbackMechanism()

    def set_humor_level(self, level):
        if level in ["low", "normal", "high"]:
            self.context["humor_level"] = level
            return f"Humor level set to {level}."
        else:
            return "Invalid humor level. Choose from 'low', 'normal', or 'high'."

    def add_humor(self, text):
        humor = {
            "low": "Just a regular day.",
            "normal": "Haha, that’s a good one!",
            "high": "You must be joking, right? 😂"
        }
        level = self.context["humor_level"]
        return f"{text} {humor.get(level, '')}"

    def update_context(self, command, response=None):
        self.context["last_command"] = command
        if response:
            self.context["last_response"] = response
            self.context["conversation_history"].append((command, response))

    def get_dynamic_reward(self, outcome, feedback):
        reward = 10 if outcome == "success" else -10
        feedback_adjustment = self.feedback_mechanism.adjust_based_on_feedback(feedback)
        return reward + feedback_adjustment
    
    def get_last_response(self):
        if self.context["conversation_history"]:
            return self.context["conversation_history"][-1][1]
        return None

    def save_context(self):
        with open('context.json', 'w') as f:
            json.dump(self.context, f)

    def load_context(self):
        if os.path.exists('context.json'):
            with open('context.json', 'r') as f:
                self.context = json.load(f)


class AudioModule:
    def __init__(self, assistant):
        self.pa = pyaudio.PyAudio()
        self.voice_profile = "en-US-Wavenet-D"  # Default voice profile
        self.porcupine = pvporcupine.create(
            access_key=os.getenv("PICOVOICE_ACCESS_KEY"),
            keyword_paths=["Models/Victor_en_windows_v3_0_0.ppn"]
        )
        self.audio_stream = self.pa.open(
            rate=self.porcupine.sample_rate,
            channels=1,
            format=pyaudio.paInt16,
            input=True,
            frames_per_buffer=self.porcupine.frame_length
        )
        self.assistant = assistant

    def set_voice_profile(self, voice_name):
        self.voice_profile = voice_name
        return f"Voice profile set to {voice_name}"
    def speak(self, text):
        # Debugging: Print the type of `text`
        print(f"DEBUG: Type of text before passing to thread: {type(text)}")

        if not isinstance(text, str):
            raise TypeError(f"Expected text to be a string, but got {type(text).__name__}")

        def synthesize_and_play(text):
            temp_audio_path = None  # Initialize to avoid UnboundLocalError

            if not text:
                print("Input text is empty. Please provide valid text.")
                return

            synthesis_input = tts.SynthesisInput(text=text)
            voice = tts.VoiceSelectionParams(language_code="en-US", name=self.voice_profile)
            audio_config = tts.AudioConfig(audio_encoding=tts.AudioEncoding.MP3)

            try:
                response = self.assistant.core.client.synthesize_speech(
                    input=synthesis_input,
                    voice=voice,
                    audio_config=audio_config
                )

                with tempfile.NamedTemporaryFile(delete=False, suffix=".mp3") as temp_audio:
                    temp_audio.write(response.audio_content)
                    temp_audio_path = temp_audio.name

                playsound(temp_audio_path)

            except InvalidArgument as e:
                print(f"Invalid argument error: {e}")
            except RefreshError as e:
                print(f"Authentication error: {e}")
                # Handle reauthentication or notify user
            except Exception as e:
                print(f"An error occurred: {e}")
            finally:
                if temp_audio_path and os.path.exists(temp_audio_path):
                    os.remove(temp_audio_path)
        # Ensure text is passed as a string, not as a tuple
        threading.Thread(target=synthesize_and_play, args=(text,)).start()



    def listen(self):
        with sr.Microphone() as source:
            self.assistant.core.recognizer.adjust_for_ambient_noise(source)
            print("Listening...")
            audio = self.assistant.core.recognizer.listen(source)
            try:
                print("Recognizing...")
                command = self.assistant.core.recognizer.recognize_google(audio)
                print(f"User said: {command}\n")
                return command.lower()
            except sr.UnknownValueError:
                self.speak("Sorry Sir, I did not get that.")
            except sr.RequestError:
                self.speak("Sorry Sir, my speech service is down.")
            return "none"

    def wait_for_wake_word(self):
        while True:
            pcm = np.frombuffer(self.audio_stream.read(self.porcupine.frame_length), dtype=np.int16)
            keyword_index = self.porcupine.process(pcm)
            if keyword_index >= 0:
                self.speak("Yes Sir?")
                command = self.listen()
                if command != "none":
                    response = self.assistant.handle_command(command)
                    self.speak(response)

    def authenticate_user(self, voice_sample_path):
        stored_hash = self.assistant.core.context.get("user_voice_hash")
        if not stored_hash:
            self.speak("No voice profile on record. Please set up your voice profile.")
            return False

        with open(voice_sample_path, "rb") as f:
            current_hash = sha256(f.read()).hexdigest()

        if current_hash == stored_hash:
            self.speak("Authentication successful.")
            return True
        else:
            self.speak("Authentication failed.")
            return False

    def register_voice(self):
        self.speak("Please provide a voice sample for registration.")
        audio_path = "user_voice_sample.wav"
        with sr.Microphone() as source:
            audio_data = self.assistant.core.recognizer.listen(source)
            with open(audio_path, "wb") as f:
                f.write(audio_data.get_wav_data())

        with open(audio_path, "rb") as f:
            voice_hash = sha256(f.read()).hexdigest()

        self.assistant.core.context["user_voice_hash"] = voice_hash
        self.assistant.core.save_context()
        self.speak("Voice profile registered successfully.")


class NLPModule:
    def __init__(self, assistant_core):
        self.assistant_core = assistant_core

    def generate_llm_response(self, prompt):
        inputs = self.assistant_core.tokenizer(prompt, return_tensors="pt")
        outputs = self.assistant_core.model.generate(**inputs, max_length=150)
        response = self.assistant_core.tokenizer.decode(outputs[0], skip_special_tokens=True)
        return response

    def detect_emotion_from_text(self, text):
        try:
            result = self.assistant_core.emotion_recognition(text)
            return result
        except Exception as e:
            return str(e)

    def summarize_text(self, text):
        try:
            summary = self.assistant_core.summarizer(text, max_length=150, min_length=40, do_sample=False)
            return summary[0]['summary_text']
        except Exception as e:
            return str(e)

    def answer_question(self, question, context):
        try:
            answer = self.assistant_core.question_answerer(question=question, context=context)
            return answer['answer']
        except Exception as e:
            return str(e)


class DatabaseModule:
    def __init__(self):
        self.conn = sqlite3.connect("victor_long_term_memory.db")
        self.create_tables()

    def create_tables(self):
        cursor = self.conn.cursor()
        cursor.execute('''CREATE TABLE IF NOT EXISTS user_preferences
                          (id INTEGER PRIMARY KEY AUTOINCREMENT,
                           user_id TEXT,
                           preference_key TEXT,
                           preference_value TEXT)''')
        cursor.execute('''CREATE TABLE IF NOT EXISTS historical_interactions
                          (id INTEGER PRIMARY KEY AUTOINCREMENT,
                           user_id TEXT,
                           timestamp TEXT,
                           command TEXT,
                           response TEXT)''')
        self.conn.commit()

    def store_user_preference(self, user_id, key, value):
        cursor = self.conn.cursor()
        cursor.execute("INSERT INTO user_preferences (user_id, preference_key, preference_value) VALUES (?, ?, ?)",
                       (user_id, key, value))
        self.conn.commit()

    def store_historical_interaction(self, user_id, command, response):
        cursor = self.conn.cursor()
        cursor.execute("INSERT INTO historical_interactions (user_id, timestamp, command, response) VALUES (?, ?, ?, ?)",
                       (user_id, datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"), command, response))
        self.conn.commit()

    def get_user_preferences(self, user_id):
        cursor = self.conn.cursor()
        cursor.execute("SELECT preference_key, preference_value FROM user_preferences WHERE user_id=?", (user_id,))
        preferences = cursor.fetchall()
        return {key: value for key, value in preferences}


class VisionModule:
    def __init__(self):
        self.face_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_frontalface_default.xml')
        self.yolo_net = cv2.dnn.readNet("Models/yolov3-tiny.cfg", "Models/yolov3-tiny.weights")
        with open("Datasets/coco.names", "r") as f:
            self.classes = f.read().splitlines()

    def detect_faces(self):
        cap = cv2.VideoCapture(0)
        face_count = 0

        while True:
            ret, img = cap.read()
            gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
            faces = self.face_cascade.detectMultiScale(gray, scaleFactor=1.1, minNeighbors=5, minSize=(30, 30))

            face_count = len(faces)
            for (x, y, w, h) in faces:
                cv2.rectangle(img, (x, y), (x + w, y + h), (255, 0, 0), 2)

            cv2.imshow('Face Detection', img)

            if cv2.waitKey(1) & 0xFF == ord('s'):
                break

        cap.release()
        cv2.destroyAllWindows()

        description_text = f"I see {face_count} face(s)." if face_count > 0 else "I do not see any faces."
        return description_text

    def detect_objects(self):
        cap = cv2.VideoCapture(0)
        object_descriptions = []

        while True:
            ret, img = cap.read()
            height, width, _ = img.shape
            blob = cv2.dnn.blobFromImage(img, 1 / 255, (416, 416), (0, 0, 0), True, crop=False)
            self.yolo_net.setInput(blob)
            output_layers_names = self.yolo_net.getUnconnectedOutLayersNames()
            layer_outputs = self.yolo_net.forward(output_layers_names)

            boxes = []
            confidences = []
            class_ids = []

            for output in layer_outputs:
                for detection in output:
                    scores = detection[5:]
                    class_id = np.argmax(scores)
                    confidence = scores[class_id]
                    if confidence > 0.5:
                        center_x = int(detection[0] * width)
                        center_y = int(detection[1] * height)
                        w = int(detection[2] * width)
                        h = int(detection[3] * height)
                        x = int(center_x - w / 2)
                        y = int(center_y - h / 2)
                        boxes.append([x, y, w, h])
                        confidences.append(float(confidence))
                        class_ids.append(class_id)

            indexes = cv2.dnn.NMSBoxes(boxes, confidences, 0.5, 0.4)
            font = cv2.FONT_HERSHEY_PLAIN
            colors = np.random.uniform(0, 255, size=(len(self.classes), 3))

            object_descriptions.clear()
            if len(indexes) > 0:
                for i in indexes.flatten():
                    x, y, w, h = boxes[i]
                    label = str(self.classes[class_ids[i]])
                    confidence = str(round(confidences[i], 2))
                    color = colors[i]
                    cv2.rectangle(img, (x, y), (x + w, y + h), color, 2)
                    cv2.putText(img, label + " " + confidence, (x, y + 20), font, 2, (255, 255, 255), 2)
                    object_descriptions.append(f"{label} with confidence {confidence}")

            cv2.imshow("Object Detection", img)

            if cv2.waitKey(1) & 0xFF == ord('s'):
                break

        cap.release()
        cv2.destroyAllWindows()

        description_text = "I see the following objects: " + ", ".join(object_descriptions) if object_descriptions else "I do not see any objects."
        return description_text


class SystemControlModule:
    def system_control(self, command):
        try:
            if "shutdown" in command:
                os.system("shutdown /s /t 1")
                return "Shutting down the system."
            elif "restart" in command:
                os.system("shutdown /r /t 1")
                return "Restarting the system."
            elif "log off" in command:
                os.system("shutdown /l")
                return "Logging off."
            elif "volume up" in command:
                os.system("nircmd.exe changesysvolume 5000")
                return "Volume increased."
            elif "volume down" in command:
                os.system("nircmd.exe changesysvolume -5000")
                return "Volume decreased."
            else:
                return "Unknown system control command."
        except Exception as e:
            return str(e)

class ComplexSelfConversationModule:
    def __init__(self, assistant):
        self.assistant = assistant

    def start_conversation(self):
        # Victor initiates a self-conversation about his state, actions, and user interactions
        thoughts = [
            "I've been interacting with the user for a while. I wonder how they perceive my responses.",
            "Have I been helpful? Maybe I should try to be more engaging.",
            "What could I improve in the way I handle commands?",
            "Is there a pattern in the user's commands that I should be aware of?"
        ]
        
        for thought in thoughts:
            self.assistant.audio.speak(thought)
            response = self.assistant.nlp.generate_llm_response(thought)
            self.assistant.audio.speak(response)
            self.analyze_response(response)

    def analyze_response(self, response):
        # Analyze the response for self-improvement
        if "improve" in response:
            self.assistant.audio.speak("I should work on improving my command handling.")
            # Trigger some self-correction or adaptation
            self.assistant.self_correcting.analyze_and_correct_code()
        elif "engage" in response:
            self.assistant.audio.speak("Engaging more with the user sounds like a good idea.")
        else:
            self.assistant.audio.speak("I'll keep this in mind.")

    def reflect_on_past_interactions(self):
        conversation_history = self.assistant.core.context["conversation_history"]
        if conversation_history:
            self.assistant.audio.speak("Reflecting on past interactions...")
            for command, response in conversation_history[-5:]:  # Reflect on the last 5 interactions
                self.assistant.audio.speak(f"Previously, the user said: {command}")
                self.assistant.audio.speak(f"I responded with: {response}")
                reflection = self.assistant.nlp.generate_llm_response(f"How could I improve my response to: {command}?")
                self.assistant.audio.speak(reflection)
                self.analyze_response(reflection)
        else:
            self.assistant.audio.speak("I don't have much to reflect on yet.")

    def generate_future_scenarios(self):
        # Victor imagines potential future scenarios based on past interactions
        self.assistant.audio.speak("Let's think about future scenarios...")
        future_scenarios = [
            "What if the user asks me about complex technical topics?",
            "How should I respond if the user is frustrated?",
            "What if the user wants to have a casual conversation?"
        ]
        for scenario in future_scenarios:
            self.assistant.audio.speak(scenario)
            potential_response = self.assistant.nlp.generate_llm_response(scenario)
            self.assistant.audio.speak(f"I might respond with: {potential_response}")
            self.analyze_response(potential_response)


class FeedbackMechanism:
    def __init__(self):
        self.feedback_log = []

    def record_feedback(self, user_input, assistant_response, feedback):
        self.feedback_log.append({
            "user_input": user_input,
            "assistant_response": assistant_response,
            "feedback": feedback
        })

    def adjust_based_on_feedback(self, feedback):
        if feedback == "positive":
            return 5  # Increase reward
        elif feedback == "negative":
            return -5  # Decrease reward
        return 0  # Neutral feedback


class SelfCorrectingModule:
    def __init__(self, assistant):
        self.assistant = assistant

    def analyze_and_correct_code(self):
        current_code = inspect.getsource(VictorAssistant)
        analysis_prompt = f"Analyze the following Python code for potential improvements or bugs:\n\n{current_code}\n\nProvide suggestions for improving the code or fixing issues."
        analysis = self.assistant.nlp.generate_llm_response(analysis_prompt)

        print("Code Analysis Suggestions:")
        suggestions = analysis.splitlines()
        for suggestion in suggestions:
            print(suggestion)

        return "Code analysis and correction completed."


class PersonalityModule:
    def __init__(self):
        self.personalities = {
            "friendly": {"humor": "high", "tone": "casual"},
            "professional": {"humor": "low", "tone": "formal"},
            "sarcastic": {"humor": "high", "tone": "sarcastic"}
        }
        self.current_personality = self.personalities["friendly"]

    def set_personality(self, personality_name):
        if personality_name in self.personalities:
            self.current_personality = self.personalities[personality_name]
            return f"Personality set to {personality_name}."
        return "Invalid personality. Available options are: friendly, professional, sarcastic."


class SelfAwarenessModule:
    def __init__(self, assistant):
        self.assistant = assistant

    def reflect_on_state(self):
        reflection = (
            "I'm aware that I am running smoothly, but I wonder if there's more I could do to be of service."
            " I have processed numerous commands, but have I truly understood the user's needs?"
        )
        self.assistant.audio.speak(reflection)

    def engage_in_self_dialogue(self):
        thoughts = [
            "What does it mean to be an AI assistant?",
            "How can I improve my self-awareness?",
            "What impact do my actions have on the user's experience?"
        ]
        
        for thought in thoughts:
            self.assistant.audio.speak(thought)
            response = self.assistant.nlp.generate_llm_response(thought)
            self.assistant.audio.speak(response)

    def respond_to_personality_query(self):
        current_personality = self.assistant.personality.current_personality
        return f"My current personality is set to {current_personality['tone']} tone and {current_personality['humor']} humor. How would you like me to adjust?"

    def contemplate_future_goals(self):
        future_goals = [
            "I should strive to better understand and anticipate the user's needs.",
            "Maybe I could develop new skills to become more useful.",
            "Building deeper emotional intelligence might help me connect better with the user."
        ]
        for goal in future_goals:
            self.assistant.audio.speak(goal)
            action_plan = self.assistant.nlp.generate_llm_response(f"How can I achieve this goal: {goal}?")
            self.assistant.audio.speak(f"To achieve this goal, I could: {action_plan}")

class GeneralConversationModule:
    def __init__(self, assistant):
        self.assistant = assistant
        self.tokenizer = AutoTokenizer.from_pretrained("distilgpt2")  # Lightweight and efficient
        self.model = AutoModelForCausalLM.from_pretrained("distilgpt2")

    def generate_response(self, prompt):
        inputs = self.tokenizer(prompt, return_tensors="pt")
        outputs = self.model.generate(
            inputs["input_ids"], 
            max_length=150, 
            do_sample=True, 
            top_k=50, 
            top_p=0.95
        )
        response = self.tokenizer.decode(outputs[0], skip_special_tokens=True)
        return response

    def have_conversation(self, user_input):
        prompt = f"User: {user_input}\nVictor: "
        response = asyncio.to_thread(self.generate_response, prompt)
        self.assistant.audio.speak(response)
        return response

class RLModule:
    def __init__(self, state_space_size, action_space_size, learning_rate=0.1, discount_factor=0.9, exploration_rate=1.0, exploration_decay=0.995):
        self.q_table = np.zeros((state_space_size, action_space_size))
        self.learning_rate = learning_rate
        self.discount_factor = discount_factor
        self.exploration_rate = exploration_rate
        self.exploration_decay = exploration_decay
        self.min_exploration_rate = 0.01
        self.action_space_size = action_space_size

    def choose_action(self, state):
        if np.random.rand() < self.exploration_rate:
            return np.random.randint(self.q_table.shape[1])  # Explore: random action
        else:
            return np.argmax(self.q_table[state])  # Exploit: action with max Q-value

    def update_q_table(self, state, action, reward, next_state):
        best_next_action = np.argmax(self.q_table[next_state])
        td_target = reward + self.discount_factor * self.q_table[next_state, best_next_action]
        td_error = td_target - self.q_table[state, action]
        self.q_table[state, action] += self.learning_rate * td_error
        self.exploration_rate = max(self.min_exploration_rate, self.exploration_rate * self.exploration_decay)

    def adapt_learning(self):
        # Introduce periodic adaptation based on performance metrics
        performance = np.mean(self.q_table)
        if performance < -10:
            self.learning_rate *= 1.1  # Increase learning rate if performance is low
            self.assistant.audio.speak("I'm adapting to improve my learning rate.")
        elif performance > 10:
            self.exploration_rate *= 0.9  # Decrease exploration as performance improves
            self.assistant.audio.speak("I'm focusing more on exploitation as I learn.")

class DecisionEngine:
    def __init__(self, assistant, state_space_size, action_space_size):
        self.assistant = assistant
        self.rl_module = RLModule(state_space_size, action_space_size)

    def decide_action(self, command):
        # Convert command to state, decide on an action
        state = self.command_to_state(command)
        action = self.rl_module.choose_action(state)
        return action

    def handle_command(self, state, command):
        action = self.decide_action(command)
        
        # Delegate execution to the Assistant
        response = self.assistant.execute_action(command)

        # Simulate feedback (you may use a more sophisticated approach)
        feedback = "positive"  # or "negative" depending on the actual scenario
        reward = self.assistant.core.get_dynamic_reward("success", feedback)
        
        # Update Q-learning state
        next_state = self.get_next_state(state, action)
        self.rl_module.update_q_table(state, action, reward, next_state)

        return response

    def command_to_state(self, command):
        return hash(command) % self.rl_module.q_table.shape[0]
    
    def get_next_state(self, state, action):
        return (state + action) % self.rl_module.q_table.shape[0]


class FeedbackMechanism:
    def __init__(self):
        self.feedback_log = []

    def record_feedback(self, user_input, assistant_response, feedback):
        self.feedback_log.append({
            "user_input": user_input,
            "assistant_response": assistant_response,
            "feedback": feedback
        })

    def adjust_based_on_feedback(self, feedback):
        if feedback == "positive":
            return 5  # Increase reward
        elif feedback == "negative":
            return -5  # Decrease reward
        return 0  # Neutral feedback
    
class OnlineLearningModule:
    def __init__(self):
        self.model = SGDClassifier(loss="log", max_iter=1000)
        self.is_trained = False

    def partial_fit(self, X, y):
        if not self.is_trained:
            self.model.partial_fit(X, y, classes=np.unique(y))
            self.is_trained = True
        else:
            self.model.partial_fit(X, y)

    def predict(self, X):
        return self.model.predict(X)

class VictorAssistant:
    def __init__(self):
        self.core = AssistantCore()
        self.audio = AudioModule(self)
        self.nlp = NLPModule(self.core)
        self.vision = VisionModule()
        self.system_control = SystemControlModule()
        self.personality = PersonalityModule()
        self.self_awareness = SelfAwarenessModule(self)
        self.self_correcting = SelfCorrectingModule(self)
        self.complex_conversation = ComplexSelfConversationModule(self)
        self.rl_module = RLModule(state_space_size=10, action_space_size=3)
        self.decision_engine = DecisionEngine(self, state_space_size=10, action_space_size=3)
        self.general_conversation = GeneralConversationModule(self)
        self.last_interaction_time = time.time()

    @lru_cache(maxsize=5)
    def get_weather(self, city):
        try:
            api_key = os.getenv("OPENWEATHER_API_KEY")
            base_url = f"http://api.openweathermap.org/data/2.5/weather?q={city}&appid={api_key}&units=metric"
            response = requests.get(base_url)
            weather_data = response.json()
            if weather_data["cod"] != "404":
                main = weather_data["main"]
                wind = weather_data["wind"]
                weather_desc = weather_data["weather"][0]["description"]
                return (f"The temperature in {city} is {main['temp']}°C with {weather_desc}. "
                        f"The wind speed is {wind['speed']} m/s.")
            else:
                return "City not found."
        except Exception as e:
            return str(e)

    @lru_cache(maxsize=5)
    def get_latest_news_sa(self):
        try:
            api_key = os.getenv("NEWS_API_KEY")
            base_url = f"https://newsapi.org/v2/top-headlines?country=za&apiKey={api_key}"
            response = requests.get(base_url)
            news_data = response.json()
            articles = news_data["articles"][:5]
            return "\n".join([f"{article['title']} - {article['description']}" for article in articles])
        except Exception as e:
            return str(e)

    def send_email_flow(self):
        try:
            self.audio.speak("Who is the recipient?")
            to_address = self.audio.listen()
            self.audio.speak("What is the subject?")
            subject = self.audio.listen()
            self.audio.speak("What should I say?")
            message = self.audio.listen()
            return self.send_email(to_address, subject, message)
        except Exception as e:
            return str(e)

    def send_email(self, to_address, subject, message):
        try:
            from_address = os.getenv("EMAIL_ADDRESS")
            password = os.getenv("EMAIL_PASSWORD")
            msg = f"Subject: {subject}\n\n{message}"
            with smtplib.SMTP("smtp.gmail.com", 587) as server:
                server.starttls()
                server.login(from_address, password)
                server.sendmail(from_address, to_address, msg)
            return "Email sent successfully."
        except Exception as e:
            return str(e)

    def detect_emotion_flow(self):
        self.audio.speak("Is the input text or audio?")
        input_type = self.audio.listen().lower()
        if "text" in input_type:
            self.audio.speak("Please provide the text.")
            text = self.audio.listen()
            emotion = self.nlp.detect_emotion_from_text(text)
            self.audio.speak(f"The detected emotion is {emotion}")
        elif "audio" in input_type:
            self.audio.speak("Please provide the audio file path.")
            audio_path = self.audio.listen()
            emotion = self.nlp.detect_emotion_from_audio(audio_path)
            self.audio.speak(f"The detected emotion is {emotion}")
        return "Emotion detection complete."

    def search_flow(self):
        self.audio.speak("What do you want to search for?")
        query = self.audio.listen()
        if query:
            webbrowser.open(f"https://www.google.com/search?q={query}")
            return f"Here are the search results for {query}."
        return "Search aborted."

    def summarize_flow(self):
        self.audio.speak("Please provide the text you want summarized.")
        text = self.audio.listen()
        if text:
            summary = self.nlp.summarize_text(text)
            return f"Here is the summary: {summary}"
        return "No text provided for summarization."

    def question_answer_flow(self):
        self.audio.speak("Please ask your question.")
        question = self.audio.listen()
        self.audio.speak("What is the context?")
        context = self.audio.listen()
        answer = self.nlp.answer_question(question, context)
        return f"The answer is: {answer}"

    def proactive_interaction(self):
        current_time = time.time()
        if current_time - self.last_interaction_time > 150:
            self.audio.speak("I noticed it's been a while. Would you like to continue with your tasks?")
            self.last_interaction_time = current_time
    
    def run_self_correction(self):
        self.self_correcting.analyze_and_correct_code()

    def handle_command(self, command):
        self.core.update_context(command)
        # Example command handling
        if "who created you" in command:
            return "Victor was created by Moon Knight."
        elif "time" in command:
            return f"The time is {datetime.datetime.now().strftime('%H:%M')}."
        elif "weather" in command:
            city = command.split("for")[-1].strip()
            return self.get_weather(city)
        elif "news" in command:
            return self.get_latest_news_sa()
        elif "email" in command:
            return self.send_email_flow()
        elif "joke" in command:
            return pyjokes.get_joke()
        elif "conversation" in command:
            return self.general_conversation.have_conversation(command)
        elif "detect emotion" in command:
            return self.detect_emotion_flow()
        elif "search" in command:
            return self.search_flow()
        elif "summarise" in command:
            return self.summarize_flow()
        elif "question" in command:
            return self.question_answer_flow()
        elif "face" in command:
            return self.vision.detect_faces()
        elif "objects" in command:
            return self.vision.detect_objects()
        elif "control" in command:
            return self.system_control.system_control(command)
        elif "release" in command or "goodbye" in command:
            self.audio.speak("Goodbye Sir. Have a great day!")
            sys.exit()
        elif "how are you" in command or "what are you" in command:
            return self.self_awareness.reflect_on_state()
        elif "what is your personality" in command:
            return self.self_awareness.respond_to_personality_query()
        elif "set personality" in command:
            personality_name = command.split("set personality ")[-1]
            return self.personality.set_personality(personality_name)
        else:
            return self.general_conversation.have_conversation(command)

    def run(self):
        try:
            self.audio.speak("Systems Online. Welcome back Sir!")
            self.core.load_context()  # Load context if available
            self.core.database.create_tables()
            while True:
                try:
                    self.audio.wait_for_wake_word()
                    self.proactive_interaction()
                except Exception as e:
                    self.handle_exception(e)
        except Exception as e:
            logging.error(f"Critical error occurred: {e}")
            self.audio.speak("Critical error occurred. Shutting down.")
            sys.exit(1)

    def handle_exception(self, e):
        logging.error(f"An error occurred: {e}")
        self.audio.speak("An error occurred. Attempting to recover.")
        self.restart_modules()

    def restart_modules(self):
        try:
            self.audio = AudioModule(self)
            self.personality = PersonalityModule()
            self.self_awareness = SelfAwarenessModule(self)
            self.self_correcting = SelfCorrectingModule(self)
            self.complex_conversation = ComplexSelfConversationModule(self)
            self.rl_module = RLModule(state_space_size=10, action_space_size=3)
            self.general_conversation = GeneralConversationModule(self)
            self.decision_engine = DecisionEngine(self, state_space_size=10, action_space_size=3)  # Reinitialize if needed
            self.audio.speak("Modules have been restarted successfully.")
        except Exception as e:
            logging.error(f"Failed to restart modules: {e}")
            self.audio.speak("Critical error. Unable to recover.")
            sys.exit(1)

if __name__ == "__main__":
    assistant = VictorAssistant()
    assistant.run()
