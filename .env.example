# Victor  - Environment Configuration Template
# Copy this file to .env and fill in your values

# =============================================================================
# OLLAMA CONFIGURATION (Offline AI)
# =============================================================================
OLLAMA_MODEL=mistral:7b
OLLAMA_HOST=http://localhost:11434
OLLAMA_TIMEOUT=30

# =============================================================================
# OPTIONAL API KEYS (Leave empty for offline mode)
# =============================================================================

# Weather API (for weather commands)
OPENWEATHER_API_KEY=

# News API (for news commands)
NEWS_API_KEY=

# Picovoice (for wake word detection)
PICOVOICE_ACCESS_KEY=

# Google Cloud (for  TTS - optional)
GOOGLE_APPLICATION_CREDENTIALS=

# =============================================================================
# PERFORMANCE SETTINGS (8GB RAM Optimized)
# =============================================================================
MAX_MEMORY_USAGE=6442450944
ENABLE_GPU=false
MODEL_CACHE_SIZE=512
TORCH_NUM_THREADS=4
OMP_NUM_THREADS=4

# =============================================================================
# SECURITY SETTINGS
# =============================================================================
ENABLE_AUTHENTICATION=true
AUTHENTICATION_METHOD=voice
SAFE_MODE=true
ENABLE_SYSTEM_COMMANDS=true
BLOCKED_COMMANDS=rm -rf,format,del /f

# =============================================================================
# PERSONALITY SETTINGS
# =============================================================================
PERSONALITY_LEARNING=true
PROACTIVE_INSIGHTS=true
MOOD_ADAPTATION=true
PERSONALITY_EVOLUTION_RATE=0.1

# =============================================================================
# AUDIO SETTINGS
# =============================================================================
AUDIO_INPUT_DEVICE=default
AUDIO_OUTPUT_DEVICE=default
WAKE_WORD_SENSITIVITY=0.5
VOICE_RECOGNITION_TIMEOUT=5
TTS_VOICE_PROFILE=en-US-Wavenet-D

# =============================================================================
# VISION SETTINGS
# =============================================================================
CAMERA_INDEX=0
FACE_RECOGNITION_THRESHOLD=0.6
OBJECT_DETECTION_CONFIDENCE=0.5
ENABLE_FACE_RECOGNITION=true
ENABLE_OBJECT_DETECTION=true

# =============================================================================
# LOGGING SETTINGS
# =============================================================================
LOG_LEVEL=INFO
LOG_FILE_MAX_SIZE=10485760
LOG_BACKUP_COUNT=5
ENABLE_DEBUG_LOGGING=false

# =============================================================================
# PATHS (Relative to project root)
# =============================================================================
DATA_PATH=data
MODELS_PATH=assets/models
LOGS_PATH=logs
CONFIG_PATH=src/config
