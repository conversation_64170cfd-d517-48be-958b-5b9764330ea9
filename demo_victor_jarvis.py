#!/usr/bin/env python3
"""
<PERSON> - JARVIS-like De<PERSON>
Demonstrates <PERSON>'s new offline AI capabilities, personality, and learning
"""

import asyncio
import logging
import sys
from pathlib import Path

# Add project root to path
sys.path.insert(0, str(Path(__file__).parent))

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def demo_ollama_conversation():
    """Demo Victor's Ollama-based conversation abilities"""
    print("\n🧠 DEMO: Ollama-based Conversation")
    print("=" * 50)
    
    try:
        from config.settings import VictorConfig
        from modules.ollama_nlp_module import OllamaNLPProcessor
        
        config = VictorConfig()
        nlp = OllamaNLPProcessor(config)
        
        # Test conversations
        conversations = [
            "Hello Victor, how are you today?",
            "What do you think about artificial intelligence?",
            "Can you help me with a programming problem?",
            "Tell me something interesting about space exploration",
            "What's your opinion on the future of technology?"
        ]
        
        for question in conversations:
            print(f"\n👤 User: {question}")
            
            # Analyze the command
            analysis = await nlp.analyze_command(question)
            print(f"🔍 Analysis: Intent={analysis['intent']}, Topics={analysis['topics']}")
            
            # Generate response
            response = await nlp.generate_response(question, analysis)
            print(f"🤖 Victor: {response}")
            
            # Small delay for realism
            await asyncio.sleep(1)
        
        await nlp.shutdown()
        print("\n✅ Ollama conversation demo completed")
        
    except Exception as e:
        print(f"❌ Ollama demo failed: {e}")
        print("Make sure Ollama is installed and Mistral model is available")

async def demo_personality_system():
    """Demo Victor's personality and learning system"""
    print("\n🎭 DEMO: Personality and Learning System")
    print("=" * 50)
    
    try:
        from config.settings import VictorConfig
        from modules.personality_module import PersonalityEngine
        
        config = VictorConfig()
        personality = PersonalityEngine(config)
        
        print("🧠 Initial Personality State:")
        summary = await personality.get_personality_summary()
        print(summary)
        
        # Simulate interactions and personality adaptation
        print("\n🔄 Simulating user interactions...")
        
        interactions = [
            {'type': 'helpful', 'satisfaction': 0.9, 'topics': ['programming', 'AI']},
            {'type': 'witty', 'satisfaction': 0.7, 'topics': ['technology']},
            {'type': 'analytical', 'satisfaction': 0.95, 'topics': ['science', 'space']},
            {'type': 'helpful', 'satisfaction': 0.8, 'topics': ['automation']}
        ]
        
        for i, interaction in enumerate(interactions, 1):
            print(f"\n📊 Interaction {i}: {interaction}")
            await personality.adapt_personality(interaction)
            
            # Show personality changes
            style = personality.get_personality_response_style()
            print(f"   Response style: Enthusiasm={style['enthusiasm']:.2f}, "
                  f"Formality={style['formality']:.2f}, "
                  f"Humor={style['humor_probability']:.2f}")
        
        # Test mood changes
        print("\n🎭 Testing mood changes...")
        moods = ['analytical', 'witty', 'encouraging', 'focused']
        
        for mood in moods:
            await personality.change_mood(mood, f"Demo mood change to {mood}")
            print(f"   Mood changed to: {mood}")
        
        # Generate proactive insight
        print("\n💡 Generating proactive insight...")
        insight = await personality.generate_proactive_insight({
            'recent_topics': ['AI', 'programming', 'technology'],
            'interaction_count': 4
        })
        
        if insight:
            print(f"   Victor's insight: {insight}")
        else:
            print("   No insight generated this time")
        
        print("\n📈 Final Personality State:")
        final_summary = await personality.get_personality_summary()
        print(final_summary)
        
        await personality.shutdown()
        print("\n✅ Personality demo completed")
        
    except Exception as e:
        print(f"❌ Personality demo failed: {e}")

async def demo_system_integration():
    """Demo Victor's system integration capabilities"""
    print("\n💻 DEMO: System Integration")
    print("=" * 50)
    
    try:
        from config.settings import VictorConfig
        from modules.system_module import SystemCommandModule
        from modules.ollama_nlp_module import OllamaNLPProcessor
        
        config = VictorConfig()
        system = SystemCommandModule(config)
        nlp = OllamaNLPProcessor(config)
        
        # Test system commands
        commands = [
            "show me system information",
            "list the files in my home directory",
            "what processes are running",
            "check network status"
        ]
        
        for command in commands:
            print(f"\n👤 User: {command}")
            
            # Analyze command
            analysis = await nlp.analyze_command(command)
            
            # Execute system command
            if analysis['requires_action']:
                result = await system.execute_command(analysis)
                print(f"🖥️  System: {result}")
            else:
                print("🔍 Command analyzed but no system action required")
        
        await system.shutdown()
        await nlp.shutdown()
        print("\n✅ System integration demo completed")
        
    except Exception as e:
        print(f"❌ System integration demo failed: {e}")

async def demo_learning_capabilities():
    """Demo Victor's learning and knowledge capabilities"""
    print("\n📚 DEMO: Learning and Knowledge System")
    print("=" * 50)
    
    try:
        from config.settings import VictorConfig
        from modules.knowledge_module import IntelligentKnowledgeBase
        from modules.ollama_nlp_module import OllamaNLPProcessor
        
        config = VictorConfig()
        knowledge = IntelligentKnowledgeBase(config)
        nlp = OllamaNLPProcessor(config)
        
        # Store some knowledge
        print("📝 Teaching Victor some facts...")
        
        facts = [
            ("Python programming", "Python is a versatile programming language known for its simplicity and readability. It's widely used in AI, web development, and data science."),
            ("Machine Learning", "Machine learning is a subset of AI that enables computers to learn and improve from experience without being explicitly programmed."),
            ("Space exploration", "Space exploration involves the discovery and exploration of celestial structures in outer space by means of space technology.")
        ]
        
        for topic, content in facts:
            await knowledge._store_knowledge(topic, content, "demo", 0.9)
            print(f"   ✅ Stored knowledge about: {topic}")
        
        # Query knowledge
        print("\n🔍 Testing knowledge retrieval...")
        
        queries = [
            "tell me about Python programming",
            "what is machine learning",
            "explain space exploration"
        ]
        
        for query in queries:
            print(f"\n👤 User: {query}")
            analysis = await nlp.analyze_command(query)
            response = await knowledge.query_knowledge(analysis)
            print(f"🧠 Victor: {response}")
        
        # Show knowledge stats
        print("\n📊 Knowledge Base Statistics:")
        stats = await knowledge.get_knowledge_stats()
        for key, value in stats.items():
            print(f"   {key}: {value}")
        
        await knowledge.shutdown()
        await nlp.shutdown()
        print("\n✅ Learning capabilities demo completed")
        
    except Exception as e:
        print(f"❌ Learning demo failed: {e}")

async def demo_full_victor_interaction():
    """Demo a full Victor interaction without audio"""
    print("\n🎯 DEMO: Full Victor Interaction (Text Mode)")
    print("=" * 50)
    
    try:
        from victor_enhanced import VictorEnhanced
        
        # Create Victor instance
        victor = VictorEnhanced()
        
        # Test commands
        commands = [
            "Hello Victor, how are you?",
            "What can you help me with?",
            "Tell me about your capabilities",
            "Show me system information",
            "What do you think about AI?",
            "Can you learn from our conversation?"
        ]
        
        print("🤖 Victor Enhanced is ready for interaction!")
        
        for command in commands:
            print(f"\n👤 User: {command}")
            
            # Process command
            response = await victor.process_command(command)
            print(f"🤖 Victor: {response}")
            
            # Small delay
            await asyncio.sleep(1)
        
        # Show Victor's personality state
        if 'personality' in victor.modules:
            print("\n🎭 Victor's Current Personality:")
            personality_summary = await victor.modules['personality'].get_personality_summary()
            print(personality_summary)
        
        await victor.shutdown()
        print("\n✅ Full interaction demo completed")
        
    except Exception as e:
        print(f"❌ Full interaction demo failed: {e}")

async def main():
    """Main demo function"""
    print("🚀 VICTOR ENHANCED - JARVIS-LIKE AI ASSISTANT DEMO")
    print("=" * 60)
    print("This demo showcases Victor's new offline AI capabilities")
    print("Optimized for 8GB RAM systems using Ollama + Mistral")
    print("=" * 60)
    
    demos = [
        ("Ollama Conversation", demo_ollama_conversation),
        ("Personality System", demo_personality_system),
        ("System Integration", demo_system_integration),
        ("Learning Capabilities", demo_learning_capabilities),
        ("Full Victor Interaction", demo_full_victor_interaction)
    ]
    
    for demo_name, demo_func in demos:
        try:
            print(f"\n🎬 Starting demo: {demo_name}")
            await demo_func()
            print(f"✅ {demo_name} completed successfully")
        except Exception as e:
            print(f"❌ {demo_name} failed: {e}")
        
        # Pause between demos
        print("\nPress Enter to continue to next demo...")
        input()
    
    print("\n🎉 ALL DEMOS COMPLETED!")
    print("=" * 60)
    print("Victor Enhanced is ready for use!")
    print("\nTo start Victor:")
    print("  python start_victor.py")
    print("\nTo install dependencies:")
    print("  python install_victor.py")

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n\n⏹️  Demo interrupted by user")
    except Exception as e:
        print(f"\n\n❌ Demo failed: {e}")
        logger.error(f"Demo error: {e}")
        sys.exit(1)
