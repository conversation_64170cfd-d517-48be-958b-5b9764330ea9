# 🤖 Victor  - JARVIS-like AI Assistant

> **A comprehensive offline AI personal assistant optimized for 8GB RAM systems**

<PERSON>  is a sophisticated AI assistant inspired by JARVIS, featuring offline operation, personality learning, system integration, and  conversational abilities using Ollama + Mistral.

## 🚀 Quick Start

### **One-Command Launch**
```bash
python victor.py          # Start Victor 
python victor.py demo     # Run interactive demo
python victor.py test     # Test system
python victor.py install  # Install dependencies
```

### **Traditional Launch**
```bash
# Install dependencies
python scripts/install_victor.py

# Run tests
python scripts/test_victor.py

# Start Victor
python scripts/start_victor.py
```

## 📁 Project Structure

```
Victor-/
├── 🤖 victor.py                     # Main launcher
├── 📄 requirements.txt              # Dependencies
├── 📄 .env.example                 # Environment template
├── 📄 .gitignore                   # Git ignore rules
│
├── 📁 src/                          # Source code
│   ├── 📄 victor_.py        # Main application
│   ├── 📁 modules/                  # Core modules
│   │   ├── 🎤 audio_module.py      # Audio processing
│   │   ├── 👁️ vision_module.py      # Computer vision
│   │   ├── 🧠 ollama_nlp_module.py # Offline AI (Mistral)
│   │   ├── 🎭 personality_module.py # JARVIS personality
│   │   ├── 📚 knowledge_module.py   # Learning system
│   │   ├── 🔒 security_module.py    # Authentication
│   │   └── 💻 system_module.py      # System integration
│   └── 📁 config/                   # Configuration
│       ├── 📄 settings.py
│       └── 📄 victor_config.json
│
├── 📁 scripts/                      # Utility scripts
│   ├── 🚀 start_victor.py          # Main launcher
│   ├── 📦 install_victor.py        # Installation
│   └── 🧪 test_victor.py           # Test suite
│
├── 📁 demos/                        # Demo applications
│   ├── 🎬 demo_victor_jarvis.py    # Main demo
│   └── 📁 examples/                # Usage examples
│
├── 📁 data/                         # Data storage
│   ├── 📁 models/                  # AI models
│   ├── 📁 faces/                   # Face recognition
│   ├── 📁 captures/                # Image captures
│   └── 📁 databases/               # Database files
│
├── 📁 assets/                       # Static assets
│   └── 📁 models/                  # Model files
│
├── 📁 logs/                         # Log files
└── 📁 docs/                         # Documentation
```

## ✨ Features

### **🧠 Offline AI Brain**
- **Mistral 7B** via Ollama - No internet required
- **8GB RAM optimized** - Efficient memory usage
- **Context awareness** - Remembers conversations
- **Learning capabilities** - Improves over time

### **🎭 JARVIS-like Personality**
- **Adaptive personality** - Learns your preferences
- **Proactive insights** - Offers helpful suggestions
- **Mood adaptation** - Adjusts to your interaction style
- **Loyalty traits** - Develops attachment over time

### **🎤  Audio**
- **Wake word detection** - "Hey Victor"
- **Voice recognition** - Understands natural speech
- **Text-to-speech** - Multiple voice profiles
- **Audio authentication** - Voice-based security

### **👁️ Computer Vision**
- **Face recognition** - Identifies known users
- **Object detection** - Sees and describes environment
- **Pose estimation** - Understands body language
- **Scene analysis** - Comprehensive visual understanding

### **💻 System Integration**
- **File operations** - Manages your files
- **Process control** - Monitors system resources
- **Network operations** - Checks connectivity
- **Safe mode** - Protects against dangerous commands

## 🛠️ Installation

### **Automatic Installation**
```bash
python scripts/install_victor.py
```

### **Manual Installation**
```bash
# 1. Install Python dependencies
pip install -r requirements.txt

# 2. Install Ollama
curl -fsSL https://ollama.ai/install.sh | sh

# 3. Download Mistral model
ollama pull mistral:7b

# 4. Install spaCy model
python -m spacy download en_core_web_sm

# 5. Setup environment
cp .env.example .env
```

## ⚙️ Configuration

### **Environment Variables**
Copy `.env.example` to `.env` and configure:

```bash
# Ollama Configuration
OLLAMA_MODEL=mistral:7b
OLLAMA_HOST=http://localhost:11434

# Optional API Keys
PICOVOICE_ACCESS_KEY=your_key_here
OPENWEATHER_API_KEY=your_key_here

# Performance (8GB RAM optimized)
MAX_MEMORY_USAGE=6442450944
ENABLE_GPU=false
```

### **Victor Configuration**
Edit `src/config/victor_config.json` for  settings.

## 🎯 Usage Examples

### **Basic Interaction**
```python
from src.victor_ import Victor

# Create Victor instance
victor = Victor()

# Process commands
response = await victor.process_command("What time is it?")
print(response)
```

### **Voice Interaction**
```bash
# Start with voice
python victor.py start

# Victor will listen for "Hey Victor" wake word
# Then respond to your voice commands
```

## 🧪 Testing

```bash
# Run all tests
python victor.py test

# Test specific components
python scripts/test_victor.py
```

## 🎬 Demo

```bash
# Interactive demo
python victor.py demo

# Shows all Victor capabilities
```

## 📊 System Requirements

- **Python 3.8+**
- **6GB+ RAM** (8GB recommended)
- **10GB+ free disk space**
- **Linux/macOS/Windows**

## 🤝 Contributing

Victor  is designed to be modular and extensible. Each module in `src/modules/` can be  independently.

## 📄 License

Open source - see individual files for specific licenses.

---

**🎉 Victor  - Your offline JARVIS-like AI companion!**
