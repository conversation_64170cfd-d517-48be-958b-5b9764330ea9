#!/usr/bin/env python3
"""
Victor  Test Script
Test basic functionality and module imports
"""

import sys
import asyncio
import logging
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root / "src"))

# Setup basic logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_imports():
    """Test if all modules can be imported"""
    print("Testing module imports...")

    try:
        # Test core modules
        from src.config.settings import VictorConfig
        print("✓ Configuration module imported successfully")

        from src.modules.audio_module import AudioProcessor
        print("✓ Audio module imported successfully")

        from src.modules.vision_module import VisionSystem
        print("✓ Vision module imported successfully")

        from src.modules.nlp_module import NLPProcessor
        print("✓ NLP module imported successfully")

        from src.modules.system_module import SystemCommandModule
        print("✓ System module imported successfully")

        from src.modules.knowledge_module import IntelligentKnowledgeBase
        print("✓ Knowledge module imported successfully")

        from src.modules.security_module import SecurityManager
        print("✓ Security module imported successfully")

        from src.victor_ import Victor
        print("✓ Main Victor module imported successfully")

        return True

    except ImportError as e:
        print(f"✗ Import error: {e}")
        return False
    except Exception as e:
        print(f"✗ Unexpected error: {e}")
        return False

def test_configuration():
    """Test configuration loading"""
    print("\nTesting configuration...")

    try:
        from src.config.settings import VictorConfig

        # Test default config creation
        config = VictorConfig()
        print("✓ Default configuration created")

        # Test config serialization
        config_dict = config.to_dict()
        print("✓ Configuration serialization works")

        # Test config validation
        is_valid = config.validate()
        if is_valid:
            print("✓ Configuration validation passed")
        else:
            print("⚠ Configuration validation failed (some features may not work)")

        return True

    except Exception as e:
        print(f"✗ Configuration test failed: {e}")
        return False

async def test_basic_functionality():
    """Test basic Victor functionality"""
    print("\nTesting basic functionality...")

    try:
        from src.victor_ import Victor

        # Create Victor instance
        victor = Victor()
        print("✓ Victor instance created")

        # Test command processing (without audio)
        test_command = "what time is it"
        response = await victor.process_command(test_command)
        print(f"✓ Command processing works: '{test_command}' -> '{response[:50]}...'")

        # Test context saving
        await victor.save_context()
        print("✓ Context saving works")

        return True

    except Exception as e:
        print(f"✗ Basic functionality test failed: {e}")
        return False

def test_dependencies():
    """Test optional dependencies"""
    print("\nTesting optional dependencies...")

    dependencies = {
        'speech_recognition': 'Speech recognition',
        'cv2': 'OpenCV computer vision',
        'numpy': 'NumPy numerical computing',
        'requests': 'HTTP requests',
        'spacy': 'spaCy NLP',
        'transformers': 'Hugging Face Transformers',
        'openai': 'OpenAI API',
        'psutil': 'System utilities',
        'sqlite3': 'SQLite database'
    }

    available = []
    missing = []

    for module, description in dependencies.items():
        try:
            __import__(module)
            available.append(f"✓ {description}")
        except ImportError:
            missing.append(f"✗ {description}")

    print("Available dependencies:")
    for dep in available:
        print(f"  {dep}")

    if missing:
        print("\nMissing optional dependencies:")
        for dep in missing:
            print(f"  {dep}")
        print("\nSome features may not be available.")

    return len(missing) == 0

def test_directories():
    """Test directory structure"""
    print("\nTesting directory structure...")

    required_dirs = [
        "src/config",
        "src/modules",
        "logs",
        "data",
        "scripts",
        "demos"
    ]

    all_exist = True

    for directory in required_dirs:
        path = Path(directory)
        if path.exists():
            print(f"✓ {directory}/ exists")
        else:
            print(f"✗ {directory}/ missing")
            all_exist = False
            # Create missing directory
            path.mkdir(parents=True, exist_ok=True)
            print(f"  Created {directory}/")

    return all_exist

async def main():
    """Main test function"""
    print("=" * 60)
    print("Victor  - System Test")
    print("=" * 60)

    tests = [
        ("Directory Structure", test_directories),
        ("Module Imports", test_imports),
        ("Configuration", test_configuration),
        ("Dependencies", test_dependencies),
        ("Basic Functionality", test_basic_functionality)
    ]

    results = []

    for test_name, test_func in tests:
        print(f"\n{test_name}:")
        print("-" * 40)

        try:
            if asyncio.iscoroutinefunction(test_func):
                result = await test_func()
            else:
                result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"✗ Test failed with exception: {e}")
            results.append((test_name, False))

    # Summary
    print("\n" + "=" * 60)
    print("Test Summary:")
    print("=" * 60)

    passed = 0
    total = len(results)

    for test_name, result in results:
        status = "PASS" if result else "FAIL"
        print(f"{test_name:.<40} {status}")
        if result:
            passed += 1

    print(f"\nTests passed: {passed}/{total}")

    if passed == total:
        print("🎉 All tests passed! Victor  is ready to use.")
        print("\nTo start Victor , run:")
        print("  python start_victor.py")
    else:
        print("⚠ Some tests failed. Please check the issues above.")
        print("\nYou may need to:")
        print("  - Install missing dependencies: pip install -r requirements.txt")
        print("  - Check your configuration files")
        print("  - Ensure all required models are downloaded")

    return passed == total

if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\nTest interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\nUnexpected error during testing: {e}")
        sys.exit(1)
