#!/usr/bin/env python3
"""
Victor  Installation Script
Automatically installs dependencies, sets up Ollama, and configures Victor
Optimized for 8GB RAM systems
"""

import os
import sys
import subprocess
import platform
import time
import requests
from pathlib import Path
import json

def print_banner():
    """Print installation banner"""
    print("=" * 70)
    print("🤖 VICTOR  - INSTALLATION SCRIPT")
    print(" AI Personal Assistant with Offline Capabilities")
    print("Optimized for 8GB RAM Systems")
    print("=" * 70)

def check_system_requirements():
    """Check system requirements"""
    print("\n📋 Checking system requirements...")

    # Check Python version
    python_version = sys.version_info
    if python_version.major < 3 or (python_version.major == 3 and python_version.minor < 8):
        print("❌ Python 3.8+ required. Current version:", sys.version)
        return False
    print(f"✅ Python {python_version.major}.{python_version.minor}.{python_version.micro}")

    # Check available RAM
    try:
        import psutil
        ram_gb = psutil.virtual_memory().total / (1024**3)
        print(f"✅ RAM: {ram_gb:.1f} GB")
        if ram_gb < 6:
            print("⚠️  Warning: Less than 6GB RAM detected. Performance may be limited.")
    except ImportError:
        print("⚠️  Could not check RAM (psutil not installed)")

    # Check disk space
    try:
        disk_free = psutil.disk_usage('.').free / (1024**3)
        print(f"✅ Free disk space: {disk_free:.1f} GB")
        if disk_free < 10:
            print("⚠️  Warning: Less than 10GB free space. May not be sufficient for models.")
    except:
        print("⚠️  Could not check disk space")

    # Check OS
    os_name = platform.system()
    print(f"✅ Operating System: {os_name}")

    return True

def install_python_dependencies():
    """Install Python dependencies with error handling"""
    print("\n📦 Installing Python dependencies...")

    # Check if we're in a virtual environment
    in_venv = hasattr(sys, 'real_prefix') or (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix)

    if not in_venv:
        print("⚠️  Not in a virtual environment!")
        print("🔧 Creating virtual environment to avoid installation issues...")

        try:
            # Create virtual environment
            venv_path = Path("venv")
            subprocess.run([sys.executable, "-m", "venv", str(venv_path)], check=True)
            print("✅ Virtual environment created")

            # Determine pip path
            if sys.platform == "win32":
                pip_path = venv_path / "Scripts" / "pip"
                python_path = venv_path / "Scripts" / "python"
            else:
                pip_path = venv_path / "bin" / "pip"
                python_path = venv_path / "bin" / "python"

            print("🚀 Please activate the virtual environment and run install again:")
            if sys.platform == "win32":
                print(f"   {venv_path}\\Scripts\\activate")
            else:
                print(f"   source {venv_path}/bin/activate")
            print("   python scripts/install_victor.py")

            return False

        except subprocess.CalledProcessError as e:
            print(f"❌ Failed to create virtual environment: {e}")
            print("💡 Please create one manually: python -m venv venv")
            return False

    try:
        # Upgrade pip first
        print("🔄 Upgrading pip...")
        subprocess.run([sys.executable, "-m", "pip", "install", "--upgrade", "pip"],
                      check=True, capture_output=True)
        print("✅ pip upgraded")

        # Try minimal requirements first
        print("🔄 Installing minimal dependencies...")
        result = subprocess.run([sys.executable, "-m", "pip", "install", "-r", "requirements-minimal.txt"],
                              capture_output=True, text=True)

        if result.returncode == 0:
            print("✅ Minimal dependencies installed")

            # Try full requirements
            print("🔄 Installing full dependencies...")
            result = subprocess.run([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"],
                                  capture_output=True, text=True)

            if result.returncode == 0:
                print("✅ All dependencies installed")
                return True
            else:
                print("⚠️  Some optional dependencies failed to install")
                print("✅ Core functionality should still work")
                return True
        else:
            print(f"❌ Failed to install minimal dependencies: {result.stderr}")
            return install_critical_packages()

    except subprocess.CalledProcessError as e:
        print(f"❌ Installation failed: {e}")
        return install_critical_packages()

def install_critical_packages():
    """Install only the most critical packages individually"""
    print("\n🔧 Installing critical packages individually...")

    critical_packages = [
        "requests",
        "numpy",
        "python-dotenv",
        "psutil",
        "rich",
        "colorama"
    ]

    success_count = 0

    for package in critical_packages:
        try:
            subprocess.run([sys.executable, "-m", "pip", "install", package],
                          check=True, capture_output=True)
            print(f"✅ {package} installed")
            success_count += 1
        except subprocess.CalledProcessError:
            print(f"❌ Failed to install {package}")

    if success_count >= 4:  # At least 4 critical packages
        print(f"✅ {success_count}/{len(critical_packages)} critical packages installed")
        print("⚠️  Victor will work with limited functionality")
        return True
    else:
        print(f"❌ Only {success_count}/{len(critical_packages)} packages installed")
        print("💡 Try running: python victor.py venv")
        return False

def install_ollama():
    """Install and setup Ollama"""
    print("\n🧠 Setting up Ollama (Offline AI)...")

    os_name = platform.system().lower()

    try:
        # Check if Ollama is already installed
        result = subprocess.run(["ollama", "--version"], capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ Ollama already installed")
            return setup_ollama_model()
    except FileNotFoundError:
        pass

    print("📥 Installing Ollama...")

    if os_name == "linux":
        # Install Ollama on Linux
        try:
            subprocess.run(["curl", "-fsSL", "https://ollama.ai/install.sh"],
                          stdout=subprocess.PIPE, check=True)
            subprocess.run(["sh"], input=subprocess.PIPE, check=True)
            print("✅ Ollama installed on Linux")
        except:
            print("❌ Failed to install Ollama automatically")
            print("Please install manually: curl -fsSL https://ollama.ai/install.sh | sh")
            return False

    elif os_name == "darwin":  # macOS
        print("📱 For macOS, please download Ollama from: https://ollama.ai/download")
        print("Or install via Homebrew: brew install ollama")
        input("Press Enter after installing Ollama...")

    elif os_name == "windows":
        print("🪟 For Windows, please download Ollama from: https://ollama.ai/download")
        input("Press Enter after installing Ollama...")

    return setup_ollama_model()

def setup_ollama_model():
    """Setup Ollama model for Victor"""
    print("\n🔧 Setting up Mistral model for Victor...")

    try:
        # Start Ollama service
        print("🚀 Starting Ollama service...")
        subprocess.Popen(["ollama", "serve"], stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
        time.sleep(5)  # Wait for service to start

        # Pull Mistral model (optimized for 8GB RAM)
        print("📥 Downloading Mistral 7B model (this may take a while)...")
        result = subprocess.run(["ollama", "pull", "mistral:7b"],
                               capture_output=True, text=True, timeout=1800)  # 30 min timeout

        if result.returncode == 0:
            print("✅ Mistral model downloaded successfully")

            # Test the model
            print("🧪 Testing Mistral model...")
            test_result = subprocess.run(
                ["ollama", "run", "mistral:7b", "Hello, respond with just 'OK'"],
                capture_output=True, text=True, timeout=60
            )

            if "OK" in test_result.stdout:
                print("✅ Mistral model working correctly")
                return True
            else:
                print("⚠️  Model test failed, but installation seems successful")
                return True
        else:
            print(f"❌ Failed to download Mistral model: {result.stderr}")
            return False

    except subprocess.TimeoutExpired:
        print("⏰ Model download timed out. You can continue later with: ollama pull mistral:7b")
        return False
    except Exception as e:
        print(f"❌ Error setting up Ollama model: {e}")
        return False

def install_spacy_model():
    """Install spaCy language model"""
    print("\n🔤 Installing spaCy language model...")

    try:
        # Install small English model for 8GB RAM optimization
        subprocess.run([sys.executable, "-m", "spacy", "download", "en_core_web_sm"],
                      check=True, capture_output=True)
        print("✅ spaCy English model installed")
        return True
    except subprocess.CalledProcessError:
        print("⚠️  Failed to install spaCy model. Victor will work without  NLP.")
        return False

def setup_directories():
    """Create necessary directories"""
    print("\n📁 Setting up directories...")

    directories = [
        "data", "data/faces", "data/captures", "data/models", "data/databases",
        "logs", "src/config", "assets/models"
    ]

    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
        print(f"✅ Created {directory}/")

    return True

def create_environment_file():
    """Create .env file with default settings"""
    print("\n⚙️  Creating environment configuration...")

    env_content = """# Victor  Environment Configuration

# Ollama Configuration
OLLAMA_MODEL=mistral:7b
OLLAMA_HOST=http://localhost:11434

# Optional API Keys (leave empty for offline mode)
OPENWEATHER_API_KEY=
NEWS_API_KEY=
PICOVOICE_ACCESS_KEY=

# Performance Settings (8GB RAM Optimized)
MAX_MEMORY_USAGE=6442450944
ENABLE_GPU=false
MODEL_CACHE_SIZE=512

# Security Settings
ENABLE_AUTHENTICATION=true
AUTHENTICATION_METHOD=voice
SAFE_MODE=true

# Personality Settings
PERSONALITY_LEARNING=true
PROACTIVE_INSIGHTS=true
MOOD_ADAPTATION=true
"""

    env_file = Path(".env")
    if not env_file.exists():
        with open(env_file, "w") as f:
            f.write(env_content)
        print("✅ Environment file created")
    else:
        print("✅ Environment file already exists")

    return True

def update_victor_config():
    """Update Victor configuration for offline mode"""
    print("\n🔧 Updating Victor configuration for offline mode...")

    config_file = Path("src/config/victor_config.json")

    if config_file.exists():
        with open(config_file, "r") as f:
            config = json.load(f)
    else:
        config = {}

    # Update for offline and 8GB RAM optimization
    config.update({
        "nlp": {
            "language_model": "ollama:mistral:7b",
            "spacy_model": "en_core_web_sm",
            "max_response_length": 150,
            "temperature": 0.7,
            "enable_context_awareness": True,
            "offline_mode": True
        },
        "system": {
            "enable_file_operations": True,
            "enable_process_control": True,
            "enable_network_operations": True,
            "enable_system_commands": True,
            "safe_mode": True,
            "max_file_size": 52428800  # 50MB for 8GB RAM
        },
        "performance": {
            "max_memory_usage": 6442450944,  # 6GB
            "enable_caching": True,
            "cache_size": 512,
            "optimize_for_ram": True
        },
        "personality": {
            "enable_learning": True,
            "enable_proactive_insights": True,
            "enable_mood_adaptation": True,
            "personality_evolution_rate": 0.1
        }
    })

    # Ensure config directory exists
    config_file.parent.mkdir(parents=True, exist_ok=True)

    with open(config_file, "w") as f:
        json.dump(config, f, indent=2)

    print("✅ Victor configuration updated")
    return True

def download_additional_models():
    """Download additional models if needed"""
    print("\n📥 Checking for additional models...")

    models_dir = Path("data/models")
    models_dir.mkdir(parents=True, exist_ok=True)

    # Note about Picovoice wake word model
    print("📝 Note: For wake word detection, you'll need to:")
    print("   1. Sign up at https://picovoice.ai/")
    print("   2. Download the Victor wake word model")
    print("   3. Place it in the assets/models/ directory")
    print("   4. Add your access key to the .env file")

    return True

def run_initial_test():
    """Run initial test of Victor"""
    print("\n🧪 Running initial Victor test...")

    try:
        # Test basic import
        result = subprocess.run([
            sys.executable, "-c",
            "from src.config.settings import VictorConfig; print('✅ Configuration OK')"
        ], capture_output=True, text=True, timeout=30)

        if result.returncode == 0:
            print("✅ Basic configuration test passed")
        else:
            print(f"⚠️  Configuration test failed: {result.stderr}")

        # Test Ollama connection
        try:
            import requests
            response = requests.get("http://localhost:11434/api/tags", timeout=5)
            if response.status_code == 200:
                print("✅ Ollama service is running")
            else:
                print("⚠️  Ollama service not responding")
        except:
            print("⚠️  Could not connect to Ollama service")

        return True

    except Exception as e:
        print(f"⚠️  Initial test encountered issues: {e}")
        return False

def main():
    """Main installation function"""
    print_banner()

    if not check_system_requirements():
        print("\n❌ System requirements not met. Please address the issues above.")
        return False

    steps = [
        ("Setting up directories", setup_directories),
        ("Installing Python dependencies", install_python_dependencies),
        ("Installing spaCy model", install_spacy_model),
        ("Installing Ollama", install_ollama),
        ("Creating environment file", create_environment_file),
        ("Updating Victor configuration", update_victor_config),
        ("Downloading additional models", download_additional_models),
        ("Running initial test", run_initial_test)
    ]

    success_count = 0

    for step_name, step_function in steps:
        print(f"\n🔄 {step_name}...")
        try:
            if step_function():
                success_count += 1
            else:
                print(f"⚠️  {step_name} completed with warnings")
        except Exception as e:
            print(f"❌ {step_name} failed: {e}")

    print("\n" + "=" * 70)
    print("🎉 VICTOR  INSTALLATION COMPLETE!")
    print(f"✅ {success_count}/{len(steps)} steps completed successfully")
    print("=" * 70)

    print("\n🚀 Next steps:")
    print("1. Start Victor: python scripts/start_victor.py")
    print("2. Or run tests: python scripts/test_victor.py")
    print("3. For wake word detection, add Picovoice key to .env")
    print("4. Customize settings in src/config/victor_config.json")

    print("\n💡 Tips for 8GB RAM systems:")
    print("- Victor is optimized for your hardware")
    print("- Close other heavy applications when using Victor")
    print("- The Mistral 7B model is lightweight but powerful")
    print("- Enable safe mode for system protection")

    return success_count >= len(steps) - 2  # Allow 2 failures

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n⏹️  Installation interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n\n❌ Installation failed with error: {e}")
        sys.exit(1)
