#!/usr/bin/env python3
"""
<PERSON> Enhanced Startup Script
Simple script to start <PERSON> with proper error handling and logging
"""

import sys
import os
import asyncio
import logging
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root / "src"))

def setup_logging():
    """Setup basic logging before <PERSON> starts"""
    log_dir = Path("logs")
    log_dir.mkdir(exist_ok=True)

    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('logs/startup.log'),
            logging.StreamHandler(sys.stdout)
        ]
    )

def check_dependencies():
    """Check if required dependencies are available"""
    # Core dependencies that are absolutely required
    core_modules = [
        'numpy',
        'requests',
        'pathlib',
        'asyncio',
        'sqlite3'
    ]

    # Optional dependencies that enhance functionality
    optional_modules = [
        'speech_recognition',
        'pyaudio',
        'cv2',  # opencv-python
        'dotenv',  # python-dotenv
        'spacy',
        'transformers'
    ]

    missing_core = []
    missing_optional = []

    # Check core modules
    for module in core_modules:
        try:
            __import__(module)
        except ImportError:
            missing_core.append(module)

    # Check optional modules
    for module in optional_modules:
        try:
            __import__(module)
        except ImportError:
            missing_optional.append(module)

    if missing_core:
        print("❌ Missing REQUIRED dependencies:")
        for module in missing_core:
            print(f"  - {module}")
        print("\n🔧 To fix this, run:")
        print("  python victor.py venv")
        print("  source venv/bin/activate")
        print("  pip install -r requirements.txt")
        return False

    if missing_optional:
        print("⚠️  Missing optional dependencies (some features may not work):")
        for module in missing_optional:
            print(f"  - {module}")
        print("\n💡 To install all features:")
        print("  python victor.py venv")
        print("  source venv/bin/activate")
        print("  pip install -r requirements.txt")

    return True

def create_directories():
    """Create necessary directories"""
    directories = [
        "logs",
        "data",
        "data/faces",
        "data/captures",
        "data/models",
        "data/databases",
        "assets/models"
    ]

    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)

def main():
    """Main startup function"""
    print("=" * 60)
    print("🤖 Victor Enhanced Personal Assistant")
    print("Version 2.0 - JARVIS Edition")
    print("=" * 60)

    # Setup logging
    setup_logging()
    logger = logging.getLogger(__name__)

    try:
        # Check dependencies
        print("Checking dependencies...")
        if not check_dependencies():
            sys.exit(1)

        # Create directories
        print("Creating directories...")
        create_directories()

        # Import and start Victor
        print("Starting Victor Enhanced...")
        from src.victor_enhanced import main as victor_main

        # Run Victor
        asyncio.run(victor_main())

    except KeyboardInterrupt:
        print("\nShutdown requested by user")
        logger.info("Victor shutdown requested by user")
    except ImportError as e:
        print(f"Import error: {e}")
        print("Please ensure all modules are properly installed")
        logger.error(f"Import error: {e}")
        sys.exit(1)
    except Exception as e:
        print(f"Unexpected error: {e}")
        logger.error(f"Unexpected error: {e}")
        sys.exit(1)
    finally:
        print("🤖 Victor Enhanced shutdown complete")

if __name__ == "__main__":
    main()
