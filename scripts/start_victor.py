#!/usr/bin/env python3
"""
<PERSON> Enhanced Startup Script
Simple script to start <PERSON> with proper error handling and logging
"""

import sys
import os
import asyncio
import logging
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root / "src"))

def setup_logging():
    """Setup basic logging before <PERSON> starts"""
    log_dir = Path("logs")
    log_dir.mkdir(exist_ok=True)

    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('logs/startup.log'),
            logging.StreamHandler(sys.stdout)
        ]
    )

def check_dependencies():
    """Check if required dependencies are available"""
    required_modules = [
        'speech_recognition',
        'pyaudio',
        'opencv-python',
        'numpy',
        'requests',
        'python-dotenv'
    ]

    missing_modules = []

    for module in required_modules:
        try:
            __import__(module.replace('-', '_'))
        except ImportError:
            missing_modules.append(module)

    if missing_modules:
        print("Missing required dependencies:")
        for module in missing_modules:
            print(f"  - {module}")
        print("\nPlease install missing dependencies using:")
        print("pip install " + " ".join(missing_modules))
        return False

    return True

def create_directories():
    """Create necessary directories"""
    directories = [
        "logs",
        "data",
        "data/faces",
        "data/captures",
        "config",
        "Models"
    ]

    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)

def main():
    """Main startup function"""
    print("=" * 60)
    print("Victor Enhanced Personal Assistant")
    print("Version 2.0 - Enhanced Edition")
    print("=" * 60)

    # Setup logging
    setup_logging()
    logger = logging.getLogger(__name__)

    try:
        # Check dependencies
        print("Checking dependencies...")
        if not check_dependencies():
            sys.exit(1)

        # Create directories
        print("Creating directories...")
        create_directories()

        # Import and start Victor
        print("Starting Victor Enhanced...")
        from src.victor_enhanced import main as victor_main

        # Run Victor
        asyncio.run(victor_main())

    except KeyboardInterrupt:
        print("\nShutdown requested by user")
        logger.info("Victor shutdown requested by user")
    except ImportError as e:
        print(f"Import error: {e}")
        print("Please ensure all modules are properly installed")
        logger.error(f"Import error: {e}")
        sys.exit(1)
    except Exception as e:
        print(f"Unexpected error: {e}")
        logger.error(f"Unexpected error: {e}")
        sys.exit(1)
    finally:
        print("Victor Enhanced shutdown complete")

if __name__ == "__main__":
    main()
