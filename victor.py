#!/usr/bin/env python3
"""
Victor - Main Launcher
Simple launcher script for Victor Personal Assistant
"""

import sys
import subprocess
import os
from pathlib import Path

def setup_virtual_environment(project_root):
    """Setup virtual environment for <PERSON>"""
    venv_path = project_root / "venv"

    if venv_path.exists():
        print("✅ Virtual environment already exists")
        print(f"📁 Location: {venv_path}")
        print("\n🚀 To activate and install dependencies:")
        print("   source venv/bin/activate  # Linux/Mac")
        print("   # or")
        print("   venv\\Scripts\\activate     # Windows")
        print("   pip install -r requirements.txt")
        return

    print("🔧 Creating virtual environment...")
    try:
        # Create virtual environment
        subprocess.run([sys.executable, "-m", "venv", str(venv_path)], check=True)
        print(f"✅ Virtual environment created at: {venv_path}")

        # Determine activation script path
        if sys.platform == "win32":
            activate_script = venv_path / "Scripts" / "activate"
            pip_path = venv_path / "Scripts" / "pip"
        else:
            activate_script = venv_path / "bin" / "activate"
            pip_path = venv_path / "bin" / "pip"

        print("\n🚀 Next steps:")
        print("1. Activate the virtual environment:")
        if sys.platform == "win32":
            print(f"   {venv_path}\\Scripts\\activate")
        else:
            print(f"   source {activate_script}")

        print("2. Install dependencies:")
        print("   pip install -r requirements.txt")

        print("3. Start Victor:")
        print("   python victor.py start")

    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to create virtual environment: {e}")
        print("\n💡 Alternative solutions:")
        print("1. Use pipx: pipx install -r requirements.txt")
        print("2. Use conda: conda create -n victor python=3.9")
        print("3. Use system packages with --break-system-packages flag")

def main():
    """Main launcher function"""
    print("🤖 Victor Personal Assistant")
    print("=" * 50)

    # Get the project root directory
    project_root = Path(__file__).parent

    # Check if we have arguments
    if len(sys.argv) > 1:
        command = sys.argv[1].lower()

        if command == "start":
            print("🚀 Starting Victor...")
            script_path = project_root / "scripts" / "start_victor.py"
            subprocess.run([sys.executable, str(script_path)])

        elif command == "test":
            print("🧪 Running Victor tests...")
            script_path = project_root / "scripts" / "test_victor.py"
            subprocess.run([sys.executable, str(script_path)])

        elif command == "demo":
            print("🎬 Running Victor demo...")
            script_path = project_root / "demos" / "demo_victor_jarvis.py"
            subprocess.run([sys.executable, str(script_path)])

        elif command == "install":
            print("📦 Installing Victor dependencies...")
            script_path = project_root / "scripts" / "install_victor.py"
            subprocess.run([sys.executable, str(script_path)])

        elif command == "venv":
            print("🐍 Setting up virtual environment...")
            setup_virtual_environment(project_root)

        elif command == "fix":
            print("🔧 Running installation fix...")
            script_path = project_root / "fix_installation.py"
            subprocess.run([sys.executable, str(script_path)])

        else:
            print(f"❌ Unknown command: {command}")
            show_help()
    else:
        # Default action - start Victor
        print("🚀 Starting Victor ...")
        script_path = project_root / "scripts" / "start_victor.py"
        subprocess.run([sys.executable, str(script_path)])

def show_help():
    """Show help information"""
    print("\n📋 Available commands:")
    print("  python victor.py start    - Start Victor ")
    print("  python victor.py test     - Run system tests")
    print("  python victor.py demo     - Run interactive demo")
    print("  python victor.py install  - Install dependencies")
    print("  python victor.py venv     - Setup virtual environment")
    print("  python victor.py fix      - Fix installation issues")
    print("  python victor.py          - Start Victor (default)")
    print("\n💡 Examples:")
    print("  python victor.py fix      # Fix installation errors")
    print("  python victor.py venv     # Setup virtual environment")
    print("  python victor.py demo     # Run demo")
    print("  python victor.py test     # Test system")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n⏹️  Interrupted by user")
    except Exception as e:
        print(f"\n\n❌ Error: {e}")
        sys.exit(1)
