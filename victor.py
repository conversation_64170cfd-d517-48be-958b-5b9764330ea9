#!/usr/bin/env python3
"""
Victor - Main Launcher
Simple launcher script for Victor Personal Assistant
"""

import sys
import subprocess
from pathlib import Path

def main():
    """Main launcher function"""
    print("🤖 Victor Personal Assistant")
    print("=" * 50)
    
    # Get the project root directory
    project_root = Path(__file__).parent
    
    # Check if we have arguments
    if len(sys.argv) > 1:
        command = sys.argv[1].lower()
        
        if command == "start":
            print("🚀 Starting Victor ...")
            script_path = project_root / "scripts" / "start_victor.py"
            subprocess.run([sys.executable, str(script_path)])
            
        elif command == "test":
            print("🧪 Running Victor tests...")
            script_path = project_root / "scripts" / "test_victor.py"
            subprocess.run([sys.executable, str(script_path)])
            
        elif command == "demo":
            print("🎬 Running Victor demo...")
            script_path = project_root / "demos" / "demo_victor_jarvis.py"
            subprocess.run([sys.executable, str(script_path)])
            
        elif command == "install":
            print("📦 Installing Victor dependencies...")
            script_path = project_root / "scripts" / "install_victor.py"
            subprocess.run([sys.executable, str(script_path)])
            
        else:
            print(f"❌ Unknown command: {command}")
            show_help()
    else:
        # Default action - start Victor
        print("🚀 Starting Victor ...")
        script_path = project_root / "scripts" / "start_victor.py"
        subprocess.run([sys.executable, str(script_path)])

def show_help():
    """Show help information"""
    print("\n📋 Available commands:")
    print("  python victor.py start    - Start Victor ")
    print("  python victor.py test     - Run system tests")
    print("  python victor.py demo     - Run interactive demo")
    print("  python victor.py install  - Install dependencies")
    print("  python victor.py          - Start Victor (default)")
    print("\n💡 Examples:")
    print("  python victor.py          # Start Victor")
    print("  python victor.py demo     # Run demo")
    print("  python victor.py test     # Test system")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n⏹️  Interrupted by user")
    except Exception as e:
        print(f"\n\n❌ Error: {e}")
        sys.exit(1)
