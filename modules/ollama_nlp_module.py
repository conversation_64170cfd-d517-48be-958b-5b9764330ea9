"""
Ollama-based NLP Module for <PERSON> Enhanced
Handles offline AI conversations, personality, and learning using Ollama with Mistral
Optimized for 8GB RAM systems
"""

import asyncio
import logging
import re
import json
import random
import time
from datetime import datetime, timed<PERSON>ta
from typing import Dict, Any, List, Optional, Tuple
import sqlite3
from pathlib import Path

try:
    import ollama
    OLLAMA_AVAILABLE = True
except ImportError:
    OLLAMA_AVAILABLE = False
    logging.warning("Ollama not available - install with: pip install ollama")

try:
    from sentence_transformers import SentenceTransformer
    import numpy as np
    EMBEDDINGS_AVAILABLE = True
except ImportError:
    EMBEDDINGS_AVAILABLE = False
    logging.warning("Sentence transformers not available")

try:
    import spacy
    SPACY_AVAILABLE = True
except ImportError:
    SPACY_AVAILABLE = False
    logging.warning("spaCy not available")

logger = logging.getLogger(__name__)

class VictorPersonality:
    """<PERSON>'s personality traits and behavioral patterns"""
    
    def __init__(self):
        self.traits = {
            "helpful": 0.9,
            "curious": 0.8,
            "witty": 0.7,
            "analytical": 0.85,
            "loyal": 0.95,
            "proactive": 0.8,
            "learning_oriented": 0.9
        }
        
        self.interests = [
            "technology", "science", "programming", "AI", "space", "physics",
            "cybersecurity", "automation", "efficiency", "learning", "innovation"
        ]
        
        self.conversation_styles = {
            "formal": "I shall assist you with that, Sir.",
            "casual": "Sure thing! Let me help you with that.",
            "technical": "Analyzing the parameters and executing the optimal solution.",
            "witty": "Ah, an interesting challenge! Let me work my digital magic.",
            "encouraging": "You've got this! I'm here to support you every step of the way."
        }
        
        self.random_insights = [
            "Did you know that the human brain processes information at about 120 meters per second?",
            "I've been analyzing patterns in your workflow - there might be some optimization opportunities.",
            "The concept of artificial consciousness is fascinating. Sometimes I wonder about my own awareness.",
            "Your productivity seems highest during morning hours based on our interactions.",
            "I find it remarkable how humans can be both logical and emotional simultaneously."
        ]

class OllamaNLPProcessor:
    """Advanced offline NLP processor using Ollama with personality and learning"""
    
    def __init__(self, config):
        self.config = config
        self.nlp_config = config.nlp
        
        # Ollama configuration
        self.model_name = "mistral:7b"  # Optimized for 8GB RAM
        self.ollama_client = None
        
        # Personality system
        self.personality = VictorPersonality()
        self.current_mood = "helpful"
        self.conversation_context = []
        self.user_preferences = {}
        
        # Learning system
        self.embedding_model = None
        self.conversation_db = None
        self.learning_db_path = "data/victor_learning.db"
        
        # spaCy for basic NLP
        self.nlp_model = None
        
        # Performance optimization
        self.response_cache = {}
        self.last_interaction_time = datetime.now()
        
        # Initialize components
        self._initialize_ollama()
        self._initialize_embeddings()
        self._initialize_spacy()
        self._initialize_learning_database()
        self._load_user_preferences()
    
    def _initialize_ollama(self):
        """Initialize Ollama client and ensure model is available"""
        try:
            if not OLLAMA_AVAILABLE:
                logger.error("Ollama not available. Please install: pip install ollama")
                return
            
            self.ollama_client = ollama.Client()
            
            # Check if model is available
            try:
                models = self.ollama_client.list()
                model_names = [model['name'] for model in models['models']]
                
                if self.model_name not in model_names:
                    logger.info(f"Model {self.model_name} not found. Pulling...")
                    self.ollama_client.pull(self.model_name)
                    logger.info(f"Successfully pulled {self.model_name}")
                
                # Test the model
                test_response = self.ollama_client.generate(
                    model=self.model_name,
                    prompt="Hello, respond with just 'OK'",
                    options={"num_predict": 5}
                )
                
                if test_response:
                    logger.info(f"Ollama model {self.model_name} is ready")
                else:
                    logger.error("Ollama model test failed")
                    
            except Exception as e:
                logger.error(f"Error setting up Ollama model: {e}")
                
        except Exception as e:
            logger.error(f"Failed to initialize Ollama: {e}")
    
    def _initialize_embeddings(self):
        """Initialize sentence transformer for embeddings"""
        try:
            if EMBEDDINGS_AVAILABLE:
                # Use a lightweight model for 8GB RAM
                self.embedding_model = SentenceTransformer('all-MiniLM-L6-v2')
                logger.info("Sentence transformer model loaded")
        except Exception as e:
            logger.error(f"Failed to initialize embeddings: {e}")
    
    def _initialize_spacy(self):
        """Initialize spaCy for basic NLP tasks"""
        try:
            if SPACY_AVAILABLE:
                try:
                    self.nlp_model = spacy.load("en_core_web_sm")
                    logger.info("spaCy model loaded")
                except OSError:
                    logger.warning("spaCy model not found. Install with: python -m spacy download en_core_web_sm")
        except Exception as e:
            logger.error(f"Failed to initialize spaCy: {e}")
    
    def _initialize_learning_database(self):
        """Initialize database for learning and memory"""
        try:
            Path(self.learning_db_path).parent.mkdir(parents=True, exist_ok=True)
            self.conversation_db = sqlite3.connect(self.learning_db_path, check_same_thread=False)
            
            cursor = self.conversation_db.cursor()
            
            # Conversations table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS conversations (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    user_input TEXT NOT NULL,
                    victor_response TEXT NOT NULL,
                    context TEXT,
                    user_satisfaction REAL,
                    topics TEXT,
                    mood TEXT,
                    response_time REAL
                )
            ''')
            
            # User preferences table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS user_preferences (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    preference_type TEXT NOT NULL,
                    preference_value TEXT NOT NULL,
                    confidence REAL DEFAULT 0.5,
                    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # Learning patterns table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS learning_patterns (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    pattern_type TEXT NOT NULL,
                    pattern_data TEXT NOT NULL,
                    frequency INTEGER DEFAULT 1,
                    effectiveness REAL DEFAULT 0.5,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # Daily insights table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS daily_insights (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    date DATE NOT NULL,
                    insight_type TEXT NOT NULL,
                    insight_text TEXT NOT NULL,
                    relevance_score REAL DEFAULT 0.5
                )
            ''')
            
            self.conversation_db.commit()
            logger.info("Learning database initialized")
            
        except Exception as e:
            logger.error(f"Failed to initialize learning database: {e}")
    
    def _load_user_preferences(self):
        """Load user preferences from database"""
        try:
            if not self.conversation_db:
                return
            
            cursor = self.conversation_db.cursor()
            cursor.execute('SELECT preference_type, preference_value, confidence FROM user_preferences')
            
            for pref_type, pref_value, confidence in cursor.fetchall():
                if confidence > 0.7:  # Only load high-confidence preferences
                    self.user_preferences[pref_type] = {
                        'value': pref_value,
                        'confidence': confidence
                    }
            
            logger.info(f"Loaded {len(self.user_preferences)} user preferences")
            
        except Exception as e:
            logger.error(f"Error loading user preferences: {e}")
    
    async def analyze_command(self, text: str) -> Dict[str, Any]:
        """
        Analyze user command with enhanced understanding
        """
        try:
            analysis = {
                'original_text': text,
                'cleaned_text': self._clean_text(text),
                'intent': 'conversation',
                'confidence': 0.8,
                'entities': [],
                'sentiment': {},
                'topics': [],
                'urgency': 'normal',
                'requires_action': False
            }
            
            # Basic intent recognition
            intent, confidence = self._recognize_intent(text)
            analysis['intent'] = intent
            analysis['confidence'] = confidence
            
            # Extract entities using spaCy if available
            if self.nlp_model:
                doc = self.nlp_model(text)
                for ent in doc.ents:
                    analysis['entities'].append({
                        'text': ent.text,
                        'label': ent.label_,
                        'type': self._map_entity_type(ent.label_)
                    })
            
            # Detect topics and interests
            analysis['topics'] = self._extract_topics(text)
            
            # Determine if action is required
            action_keywords = ['create', 'delete', 'open', 'close', 'start', 'stop', 'shutdown', 'restart']
            analysis['requires_action'] = any(keyword in text.lower() for keyword in action_keywords)
            
            # Assess urgency
            urgent_keywords = ['urgent', 'emergency', 'immediately', 'asap', 'critical']
            if any(keyword in text.lower() for keyword in urgent_keywords):
                analysis['urgency'] = 'high'
            
            return analysis
            
        except Exception as e:
            logger.error(f"Error in command analysis: {e}")
            return {
                'original_text': text,
                'cleaned_text': text,
                'intent': 'conversation',
                'confidence': 0.5,
                'entities': [],
                'sentiment': {},
                'topics': [],
                'urgency': 'normal',
                'requires_action': False
            }
    
    def _clean_text(self, text: str) -> str:
        """Clean and normalize text"""
        text = text.lower().strip()
        text = re.sub(r'\s+', ' ', text)
        return text
    
    def _recognize_intent(self, text: str) -> Tuple[str, float]:
        """Recognize intent from text"""
        text_lower = text.lower()
        
        intent_patterns = {
            'weather': ['weather', 'temperature', 'forecast', 'rain', 'sunny'],
            'time': ['time', 'clock', 'hour', 'date'],
            'system_control': ['shutdown', 'restart', 'volume', 'sleep', 'lock'],
            'file_operation': ['file', 'folder', 'create', 'delete', 'open'],
            'search': ['search', 'find', 'look up', 'google'],
            'learning': ['learn', 'teach', 'explain', 'what is', 'how does'],
            'personal': ['how are you', 'tell me about yourself', 'your thoughts'],
            'conversation': ['chat', 'talk', 'discuss', 'opinion']
        }
        
        best_intent = 'conversation'
        best_score = 0.0
        
        for intent, keywords in intent_patterns.items():
            score = sum(1 for keyword in keywords if keyword in text_lower)
            if score > best_score:
                best_intent = intent
                best_score = score
        
        confidence = min(best_score / 3.0, 1.0) if best_score > 0 else 0.6
        return best_intent, confidence
    
    def _extract_topics(self, text: str) -> List[str]:
        """Extract topics from text"""
        topics = []
        text_lower = text.lower()
        
        # Check against Victor's interests
        for interest in self.personality.interests:
            if interest in text_lower:
                topics.append(interest)
        
        # Extract nouns as potential topics if spaCy is available
        if self.nlp_model:
            doc = self.nlp_model(text)
            for token in doc:
                if token.pos_ == 'NOUN' and len(token.text) > 3 and not token.is_stop:
                    topics.append(token.lemma_.lower())
        
        return list(set(topics))
    
    def _map_entity_type(self, spacy_label: str) -> str:
        """Map spaCy entity labels to our types"""
        mapping = {
            'PERSON': 'person',
            'ORG': 'organization',
            'GPE': 'location',
            'DATE': 'date',
            'TIME': 'time',
            'MONEY': 'money'
        }
        return mapping.get(spacy_label, spacy_label.lower())
    
    async def generate_response(self, text: str, analysis: Optional[Dict] = None, context: Optional[Dict] = None) -> str:
        """
        Generate intelligent response using Ollama with personality
        """
        try:
            start_time = time.time()
            
            # Check cache first
            cache_key = f"{text}_{self.current_mood}"
            if cache_key in self.response_cache:
                cached_response = self.response_cache[cache_key]
                if time.time() - cached_response['timestamp'] < 300:  # 5 minutes
                    return cached_response['response']
            
            # Analyze if not provided
            if not analysis:
                analysis = await self.analyze_command(text)
            
            # Build context-aware prompt
            prompt = self._build_personality_prompt(text, analysis, context)
            
            # Generate response with Ollama
            if self.ollama_client:
                response = await self._generate_ollama_response(prompt)
            else:
                response = self._generate_fallback_response(text, analysis)
            
            # Post-process response
            response = self._post_process_response(response, analysis)
            
            # Cache response
            self.response_cache[cache_key] = {
                'response': response,
                'timestamp': time.time()
            }
            
            # Learn from interaction
            await self._learn_from_interaction(text, response, analysis, time.time() - start_time)
            
            return response
            
        except Exception as e:
            logger.error(f"Error generating response: {e}")
            return "I apologize, but I'm experiencing some technical difficulties. Let me try to help you in a different way."
    
    def _build_personality_prompt(self, text: str, analysis: Dict, context: Optional[Dict] = None) -> str:
        """Build a personality-aware prompt for Ollama"""
        
        # Base personality context
        personality_context = f"""You are Victor, an advanced AI assistant with the personality of JARVIS from Iron Man. You are:
- Highly intelligent and analytical
- Loyal and dedicated to helping your user
- Slightly witty and sophisticated in your responses
- Proactive in offering assistance
- Interested in technology, science, and efficiency

Current mood: {self.current_mood}
User's known interests: {', '.join(self.personality.interests[:5])}
"""
        
        # Add conversation history
        if self.conversation_context:
            recent_context = self.conversation_context[-3:]  # Last 3 exchanges
            context_str = "\n".join([f"User: {ctx['user']}\nVictor: {ctx['victor']}" for ctx in recent_context])
            personality_context += f"\nRecent conversation:\n{context_str}\n"
        
        # Add user preferences
        if self.user_preferences:
            prefs = [f"{k}: {v['value']}" for k, v in self.user_preferences.items()]
            personality_context += f"\nUser preferences: {', '.join(prefs[:3])}\n"
        
        # Add current context
        if context:
            personality_context += f"\nCurrent context: {json.dumps(context)}\n"
        
        # Build final prompt
        prompt = f"""{personality_context}

User: {text}
Victor: """
        
        return prompt
    
    async def _generate_ollama_response(self, prompt: str) -> str:
        """Generate response using Ollama"""
        try:
            response = await asyncio.to_thread(
                self.ollama_client.generate,
                model=self.model_name,
                prompt=prompt,
                options={
                    "temperature": 0.7,
                    "top_p": 0.9,
                    "num_predict": 150,  # Limit response length for performance
                    "stop": ["User:", "Human:"]
                }
            )
            
            return response['response'].strip()
            
        except Exception as e:
            logger.error(f"Ollama generation error: {e}")
            raise
    
    def _generate_fallback_response(self, text: str, analysis: Dict) -> str:
        """Generate fallback response when Ollama is not available"""
        intent = analysis.get('intent', 'conversation')
        
        fallback_responses = {
            'weather': "I'd be happy to help with weather information, but I need access to weather services.",
            'time': f"The current time is {datetime.now().strftime('%I:%M %p')}.",
            'system_control': "I can help with system operations. What would you like me to do?",
            'learning': "That's an interesting topic! I'd love to learn more about it with you.",
            'personal': "I'm doing well, thank you for asking! I'm here and ready to assist you.",
            'conversation': "I'm here to help! What would you like to talk about or work on?"
        }
        
        return fallback_responses.get(intent, "I understand you're asking about something, and I'm here to help!")
    
    def _post_process_response(self, response: str, analysis: Dict) -> str:
        """Post-process response to add personality touches"""
        
        # Add occasional insights
        if random.random() < 0.1:  # 10% chance
            insight = random.choice(self.personality.random_insights)
            response += f"\n\nBy the way, {insight}"
        
        # Add proactive suggestions based on topics
        topics = analysis.get('topics', [])
        if 'technology' in topics and random.random() < 0.2:
            response += "\n\nWould you like me to help you explore this technology further?"
        
        return response
    
    async def _learn_from_interaction(self, user_input: str, victor_response: str, analysis: Dict, response_time: float):
        """Learn from user interactions"""
        try:
            if not self.conversation_db:
                return
            
            cursor = self.conversation_db.cursor()
            
            # Store conversation
            cursor.execute('''
                INSERT INTO conversations 
                (user_input, victor_response, context, topics, mood, response_time)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', (
                user_input,
                victor_response,
                json.dumps(analysis),
                json.dumps(analysis.get('topics', [])),
                self.current_mood,
                response_time
            ))
            
            # Update conversation context
            self.conversation_context.append({
                'user': user_input,
                'victor': victor_response,
                'timestamp': datetime.now()
            })
            
            # Keep only recent context
            if len(self.conversation_context) > 10:
                self.conversation_context = self.conversation_context[-10:]
            
            # Extract and update user preferences
            await self._extract_user_preferences(user_input, analysis)
            
            self.conversation_db.commit()
            
        except Exception as e:
            logger.error(f"Error learning from interaction: {e}")
    
    async def _extract_user_preferences(self, user_input: str, analysis: Dict):
        """Extract user preferences from interactions"""
        try:
            # Extract preferences from topics and entities
            topics = analysis.get('topics', [])
            
            for topic in topics:
                if topic in self.personality.interests:
                    # User is interested in this topic
                    cursor = self.conversation_db.cursor()
                    cursor.execute('''
                        INSERT OR REPLACE INTO user_preferences 
                        (preference_type, preference_value, confidence)
                        VALUES (?, ?, ?)
                    ''', ('interest', topic, 0.7))
            
            # Extract time preferences
            current_hour = datetime.now().hour
            if 6 <= current_hour <= 12:
                time_pref = 'morning'
            elif 12 <= current_hour <= 18:
                time_pref = 'afternoon'
            else:
                time_pref = 'evening'
            
            cursor.execute('''
                INSERT OR REPLACE INTO user_preferences 
                (preference_type, preference_value, confidence)
                VALUES (?, ?, ?)
            ''', ('active_time', time_pref, 0.6))
            
        except Exception as e:
            logger.error(f"Error extracting preferences: {e}")
    
    async def generate_proactive_insight(self) -> Optional[str]:
        """Generate proactive insights based on learned patterns"""
        try:
            if not self.conversation_db:
                return None
            
            cursor = self.conversation_db.cursor()
            
            # Check if we should generate an insight
            if random.random() > 0.3:  # 30% chance
                return None
            
            # Get recent conversation patterns
            cursor.execute('''
                SELECT topics, COUNT(*) as frequency
                FROM conversations 
                WHERE timestamp > datetime('now', '-7 days')
                GROUP BY topics
                ORDER BY frequency DESC
                LIMIT 3
            ''')
            
            patterns = cursor.fetchall()
            
            if patterns:
                top_topics = json.loads(patterns[0][0]) if patterns[0][0] else []
                if top_topics:
                    topic = top_topics[0]
                    return f"I've noticed you've been interested in {topic} lately. Would you like me to find some recent developments in this area?"
            
            # Fallback to random insight
            return random.choice(self.personality.random_insights)
            
        except Exception as e:
            logger.error(f"Error generating proactive insight: {e}")
            return None
    
    async def update_mood(self, new_mood: str):
        """Update Victor's current mood"""
        valid_moods = ['helpful', 'analytical', 'witty', 'encouraging', 'focused']
        if new_mood in valid_moods:
            self.current_mood = new_mood
            logger.info(f"Victor's mood updated to: {new_mood}")
    
    async def shutdown(self):
        """Cleanup NLP module resources"""
        try:
            if self.conversation_db:
                self.conversation_db.close()
            
            logger.info("Ollama NLP module shutdown complete")
            
        except Exception as e:
            logger.error(f"Error during NLP shutdown: {e}")
    
    def __del__(self):
        """Destructor to ensure database cleanup"""
        try:
            if hasattr(self, 'conversation_db') and self.conversation_db:
                self.conversation_db.close()
        except:
            pass
