"""
Advanced Natural Language Processing Module for Victor Enhanced
Handles text analysis, intent recognition, entity extraction, and response generation
"""

import asyncio
import logging
import re
from typing import Dict, Any, List, Optional, Tuple
import json

try:
    import spacy
    SPACY_AVAILABLE = True
except ImportError:
    SPACY_AVAILABLE = False
    logging.warning("spaCy not available")

try:
    from transformers import (
        pipeline,
        AutoTokenizer,
        AutoModelForCausalLM,
        AutoModelForSequenceClassification
    )
    TRANSFORMERS_AVAILABLE = True
except ImportError:
    TRANSFORMERS_AVAILABLE = False
    logging.warning("Transformers not available")

try:
    import ollama
    OLLAMA_AVAILABLE = True
except ImportError:
    OLLAMA_AVAILABLE = False
    logging.warning("Ollama not available")

try:
    from sentence_transformers import SentenceTransformer
    SENTENCE_TRANSFORMERS_AVAILABLE = True
except ImportError:
    SENTENCE_TRANSFORMERS_AVAILABLE = False
    logging.warning("Sentence Transformers not available")

logger = logging.getLogger(__name__)

class AdvancedNLPProcessor:
    """Advanced NLP processing with multiple models and techniques"""

    def __init__(self, config):
        self.config = config
        self.nlp_config = config.nlp

        # Initialize models
        self.nlp_model = None
        self.tokenizer = None
        self.language_model = None
        self.sentiment_analyzer = None
        self.question_answerer = None
        self.summarizer = None

        # Intent patterns
        self.intent_patterns = self._load_intent_patterns()

        # Initialize components
        self._initialize_models()

    def _initialize_models(self):
        """Initialize NLP models"""
        try:
            # Initialize spaCy model
            if SPACY_AVAILABLE:
                try:
                    self.nlp_model = spacy.load(self.nlp_config.spacy_model)
                    logger.info(f"Loaded spaCy model: {self.nlp_config.spacy_model}")
                except OSError:
                    logger.warning(f"spaCy model {self.nlp_config.spacy_model} not found, using en_core_web_sm")
                    try:
                        self.nlp_model = spacy.load("en_core_web_sm")
                    except OSError:
                        logger.error("No spaCy model available")

            # Initialize Transformers models
            if TRANSFORMERS_AVAILABLE:
                try:
                    # Language model for generation
                    self.tokenizer = AutoTokenizer.from_pretrained(self.nlp_config.language_model)
                    self.language_model = AutoModelForCausalLM.from_pretrained(self.nlp_config.language_model)

                    # Sentiment analysis
                    self.sentiment_analyzer = pipeline(
                        "sentiment-analysis",
                        model=self.nlp_config.sentiment_model
                    )

                    # Question answering
                    self.question_answerer = pipeline(
                        "question-answering",
                        model=self.nlp_config.question_answering_model
                    )

                    # Summarization
                    self.summarizer = pipeline(
                        "summarization",
                        model=self.nlp_config.summarization_model
                    )

                    logger.info("Transformers models initialized successfully")

                except Exception as e:
                    logger.error(f"Failed to initialize Transformers models: {e}")

            # Initialize OpenAI client
            if OPENAI_AVAILABLE and self.config.api.openai_api_key:
                openai.api_key = self.config.api.openai_api_key
                logger.info("OpenAI client initialized")

        except Exception as e:
            logger.error(f"Error initializing NLP models: {e}")

    def _load_intent_patterns(self) -> Dict[str, List[str]]:
        """Load intent recognition patterns"""
        return {
            'weather': [
                r'weather.*in\s+(\w+)',
                r'what.*weather.*like',
                r'temperature.*in\s+(\w+)',
                r'forecast.*for\s+(\w+)',
                r'how.*hot.*cold.*outside'
            ],
            'time': [
                r'what.*time.*is.*it',
                r'current.*time',
                r'tell.*me.*time',
                r'what.*time'
            ],
            'system_control': [
                r'shutdown.*computer',
                r'restart.*system',
                r'turn.*off.*computer',
                r'reboot.*machine',
                r'lock.*screen',
                r'volume.*up|down|mute',
                r'sleep.*computer',
                r'hibernate'
            ],
            'file_operation': [
                r'create.*file|folder',
                r'delete.*file|folder',
                r'copy.*file|folder',
                r'move.*file|folder',
                r'list.*files|directory',
                r'show.*contents',
                r'open.*file|folder'
            ],
            'search': [
                r'search.*for',
                r'look.*up',
                r'find.*information.*about',
                r'google.*search',
                r'web.*search'
            ],
            'email': [
                r'send.*email',
                r'compose.*message',
                r'write.*email',
                r'email.*to'
            ],
            'vision': [
                r'what.*do.*you.*see',
                r'describe.*what.*you.*see',
                r'look.*at',
                r'analyze.*image',
                r'detect.*faces|objects',
                r'take.*picture'
            ],
            'learning': [
                r'learn.*about',
                r'tell.*me.*about',
                r'what.*is',
                r'explain',
                r'define',
                r'information.*about'
            ],
            'task_management': [
                r'add.*task',
                r'create.*reminder',
                r'schedule.*meeting',
                r'set.*alarm',
                r'remind.*me'
            ],
            'security': [
                r'authenticate',
                r'login',
                r'logout',
                r'change.*password',
                r'security.*check'
            ],
            'conversation': [
                r'how.*are.*you',
                r'hello|hi|hey',
                r'goodbye|bye',
                r'thank.*you',
                r'tell.*me.*joke',
                r'chat.*with.*me'
            ]
        }

    async def analyze_command(self, text: str) -> Dict[str, Any]:
        """
        Comprehensive analysis of user command

        Args:
            text: User input text

        Returns:
            dict: Analysis results including intent, entities, sentiment, etc.
        """
        try:
            analysis = {
                'original_text': text,
                'cleaned_text': self._clean_text(text),
                'intent': 'unknown',
                'confidence': 0.0,
                'entities': [],
                'sentiment': {},
                'keywords': [],
                'language': 'en'
            }

            # Clean and preprocess text
            cleaned_text = analysis['cleaned_text']

            # Intent recognition
            intent, confidence = await self._recognize_intent(cleaned_text)
            analysis['intent'] = intent
            analysis['confidence'] = confidence

            # Entity extraction
            entities = await self._extract_entities(text)
            analysis['entities'] = entities

            # Sentiment analysis
            sentiment = await self._analyze_sentiment(text)
            analysis['sentiment'] = sentiment

            # Keyword extraction
            keywords = await self._extract_keywords(text)
            analysis['keywords'] = keywords

            # Language detection (simplified)
            analysis['language'] = self._detect_language(text)

            logger.debug(f"NLP Analysis: {analysis}")
            return analysis

        except Exception as e:
            logger.error(f"Error in command analysis: {e}")
            return {
                'original_text': text,
                'cleaned_text': text,
                'intent': 'unknown',
                'confidence': 0.0,
                'entities': [],
                'sentiment': {'label': 'neutral', 'score': 0.5},
                'keywords': [],
                'language': 'en'
            }

    def _clean_text(self, text: str) -> str:
        """Clean and normalize text"""
        # Convert to lowercase
        text = text.lower()

        # Remove extra whitespace
        text = re.sub(r'\s+', ' ', text).strip()

        # Remove special characters but keep basic punctuation
        text = re.sub(r'[^\w\s\.\?\!,]', '', text)

        return text

    async def _recognize_intent(self, text: str) -> Tuple[str, float]:
        """Recognize intent from text using pattern matching"""
        try:
            best_intent = 'unknown'
            best_confidence = 0.0

            for intent, patterns in self.intent_patterns.items():
                for pattern in patterns:
                    match = re.search(pattern, text, re.IGNORECASE)
                    if match:
                        # Simple confidence based on pattern specificity
                        confidence = len(pattern) / 100.0
                        if confidence > best_confidence:
                            best_intent = intent
                            best_confidence = confidence

            # If no pattern match, try keyword-based matching
            if best_confidence == 0.0:
                intent_keywords = {
                    'weather': ['weather', 'temperature', 'forecast', 'rain', 'sunny', 'cloudy'],
                    'time': ['time', 'clock', 'hour', 'minute'],
                    'system_control': ['shutdown', 'restart', 'volume', 'sleep', 'lock'],
                    'file_operation': ['file', 'folder', 'directory', 'create', 'delete', 'copy'],
                    'search': ['search', 'find', 'look', 'google'],
                    'conversation': ['hello', 'hi', 'how', 'are', 'you', 'thanks', 'bye']
                }

                for intent, keywords in intent_keywords.items():
                    matches = sum(1 for keyword in keywords if keyword in text)
                    if matches > 0:
                        confidence = matches / len(keywords)
                        if confidence > best_confidence:
                            best_intent = intent
                            best_confidence = confidence

            return best_intent, min(best_confidence, 1.0)

        except Exception as e:
            logger.error(f"Error in intent recognition: {e}")
            return 'unknown', 0.0

    async def _extract_entities(self, text: str) -> List[Dict[str, Any]]:
        """Extract named entities from text"""
        entities = []

        try:
            if self.nlp_model:
                doc = self.nlp_model(text)
                for ent in doc.ents:
                    entities.append({
                        'text': ent.text,
                        'label': ent.label_,
                        'start': ent.start_char,
                        'end': ent.end_char,
                        'type': self._map_entity_type(ent.label_)
                    })

            # Add custom entity extraction
            custom_entities = self._extract_custom_entities(text)
            entities.extend(custom_entities)

        except Exception as e:
            logger.error(f"Error in entity extraction: {e}")

        return entities

    def _extract_custom_entities(self, text: str) -> List[Dict[str, Any]]:
        """Extract custom entities using regex patterns"""
        entities = []

        # File paths
        file_pattern = r'[a-zA-Z]:\\(?:[^\\/:*?"<>|\r\n]+\\)*[^\\/:*?"<>|\r\n]*|/(?:[^/\0]+/)*[^/\0]*'
        for match in re.finditer(file_pattern, text):
            entities.append({
                'text': match.group(),
                'label': 'FILE_PATH',
                'start': match.start(),
                'end': match.end(),
                'type': 'file_path'
            })

        # Email addresses
        email_pattern = r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'
        for match in re.finditer(email_pattern, text):
            entities.append({
                'text': match.group(),
                'label': 'EMAIL',
                'start': match.start(),
                'end': match.end(),
                'type': 'email'
            })

        # URLs
        url_pattern = r'https?://(?:[-\w.])+(?:[:\d]+)?(?:/(?:[\w/_.])*(?:\?(?:[\w&=%.])*)?(?:#(?:[\w.])*)?)?'
        for match in re.finditer(url_pattern, text):
            entities.append({
                'text': match.group(),
                'label': 'URL',
                'start': match.start(),
                'end': match.end(),
                'type': 'url'
            })

        return entities

    def _map_entity_type(self, spacy_label: str) -> str:
        """Map spaCy entity labels to our custom types"""
        mapping = {
            'PERSON': 'person',
            'ORG': 'organization',
            'GPE': 'location',
            'LOC': 'location',
            'DATE': 'date',
            'TIME': 'time',
            'MONEY': 'money',
            'QUANTITY': 'quantity',
            'ORDINAL': 'number',
            'CARDINAL': 'number'
        }
        return mapping.get(spacy_label, spacy_label.lower())

    async def _analyze_sentiment(self, text: str) -> Dict[str, Any]:
        """Analyze sentiment of text"""
        try:
            if self.sentiment_analyzer:
                result = await asyncio.to_thread(self.sentiment_analyzer, text)
                if result and len(result) > 0:
                    return {
                        'label': result[0]['label'],
                        'score': result[0]['score']
                    }
        except Exception as e:
            logger.error(f"Error in sentiment analysis: {e}")

        return {'label': 'neutral', 'score': 0.5}

    async def _extract_keywords(self, text: str) -> List[str]:
        """Extract keywords from text"""
        try:
            if self.nlp_model:
                doc = self.nlp_model(text)
                keywords = []

                for token in doc:
                    # Extract important words (nouns, verbs, adjectives)
                    if (token.pos_ in ['NOUN', 'VERB', 'ADJ'] and
                        not token.is_stop and
                        not token.is_punct and
                        len(token.text) > 2):
                        keywords.append(token.lemma_.lower())

                return list(set(keywords))  # Remove duplicates
        except Exception as e:
            logger.error(f"Error in keyword extraction: {e}")

        # Fallback: simple word extraction
        words = text.lower().split()
        stop_words = {'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'is', 'are', 'was', 'were'}
        return [word for word in words if word not in stop_words and len(word) > 2]

    def _detect_language(self, text: str) -> str:
        """Simple language detection (placeholder)"""
        # This is a simplified implementation
        # In practice, you'd use a proper language detection library
        return 'en'

    async def generate_response(self, text: str, context: Optional[Dict] = None) -> str:
        """
        Generate a response to user input

        Args:
            text: User input text
            context: Optional context information

        Returns:
            str: Generated response
        """
        try:
            # Try OpenAI first if available
            if OPENAI_AVAILABLE and self.config.api.openai_api_key:
                return await self._generate_openai_response(text, context)

            # Fallback to local language model
            if self.language_model and self.tokenizer:
                return await self._generate_local_response(text, context)

            # Final fallback to template responses
            return self._generate_template_response(text)

        except Exception as e:
            logger.error(f"Error generating response: {e}")
            return "I'm sorry, I encountered an error while processing your request."

    async def _generate_openai_response(self, text: str, context: Optional[Dict] = None) -> str:
        """Generate response using OpenAI API"""
        try:
            prompt = f"User: {text}\nAssistant: "

            if context:
                prompt = f"Context: {json.dumps(context)}\n{prompt}"

            response = await asyncio.to_thread(
                openai.Completion.create,
                engine="text-davinci-003",
                prompt=prompt,
                max_tokens=self.nlp_config.max_response_length,
                temperature=self.nlp_config.temperature
            )

            return response.choices[0].text.strip()

        except Exception as e:
            logger.error(f"OpenAI response generation error: {e}")
            raise

    async def _generate_local_response(self, text: str, context: Optional[Dict] = None) -> str:
        """Generate response using local language model"""
        try:
            prompt = f"User: {text}\nAssistant: "

            inputs = self.tokenizer(prompt, return_tensors="pt")
            outputs = await asyncio.to_thread(
                self.language_model.generate,
                **inputs,
                max_length=inputs['input_ids'].shape[1] + self.nlp_config.max_response_length,
                temperature=self.nlp_config.temperature,
                do_sample=True,
                pad_token_id=self.tokenizer.eos_token_id
            )

            response = self.tokenizer.decode(outputs[0], skip_special_tokens=True)
            # Extract only the assistant's response
            response = response.split("Assistant: ")[-1].strip()

            return response

        except Exception as e:
            logger.error(f"Local response generation error: {e}")
            raise

    def _generate_template_response(self, text: str) -> str:
        """Generate template-based response"""
        templates = {
            'greeting': "Hello! How can I assist you today?",
            'thanks': "You're welcome! Is there anything else I can help you with?",
            'goodbye': "Goodbye! Have a great day!",
            'unknown': "I'm not sure I understand. Could you please rephrase that?",
            'error': "I apologize, but I encountered an issue processing your request."
        }

        text_lower = text.lower()

        if any(word in text_lower for word in ['hello', 'hi', 'hey']):
            return templates['greeting']
        elif any(word in text_lower for word in ['thank', 'thanks']):
            return templates['thanks']
        elif any(word in text_lower for word in ['bye', 'goodbye']):
            return templates['goodbye']
        else:
            return templates['unknown']

    async def summarize_text(self, text: str, max_length: Optional[int] = None) -> str:
        """Summarize text using the summarization model"""
        try:
            if self.summarizer:
                max_len = max_length or 150
                min_len = min(40, max_len // 3)

                result = await asyncio.to_thread(
                    self.summarizer,
                    text,
                    max_length=max_len,
                    min_length=min_len,
                    do_sample=False
                )

                return result[0]['summary_text']
            else:
                return "Summarization not available."

        except Exception as e:
            logger.error(f"Error in text summarization: {e}")
            return f"Failed to summarize text: {str(e)}"

    async def answer_question(self, question: str, context: str) -> str:
        """Answer question based on context"""
        try:
            if self.question_answerer:
                result = await asyncio.to_thread(
                    self.question_answerer,
                    question=question,
                    context=context
                )

                return result['answer']
            else:
                return "Question answering not available."

        except Exception as e:
            logger.error(f"Error in question answering: {e}")
            return f"Failed to answer question: {str(e)}"

    async def shutdown(self):
        """Cleanup NLP module resources"""
        logger.info("NLP module shutdown complete")
